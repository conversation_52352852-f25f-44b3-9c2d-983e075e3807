; ===============================================================================
; INPUT/OUTPUT UTILITIES
; ===============================================================================
; This file contains utilities for handling various input and output operations
; including clipboard manipulation, input capture, and mouse interaction.
; 
; Functions included:
; - ClipSend(): Fast text sending via clipboard manipulation
; - GetInput(): Simple wrapper for InputHook functionality  
; - GetClick(): Wait for mouse click and capture coordinates
; ===============================================================================

; No external dependencies required for this module

; ===============================================================================
; CLIPSEND - FAST TEXT TRANSMISSION VIA CLIPBOARD
; ===============================================================================

/**
 * Sends text quickly by temporarily using the clipboard instead of Send().
 * This method is significantly faster than traditional Send() for large amounts of text.
 * The function preserves your original clipboard content by default.
 * 
 * Why this is faster:
 * - Send() types each character individually, which can be slow
 * - Clipboard paste (Ctrl+V or Shift+Insert) sends all text at once
 * - Most applications can process clipboard content much faster than typed input
 * 
 * @param toSend {String} The text content you want to send/paste
 * @param endChar {String} Optional character to append after the text (default: empty)
 *                         Common uses: " " for space, "`n" for newline, "`t" for tab
 * @param isClipReverted {Boolean} Whether to restore the original clipboard content (default: true)
 *                                Set to false if you want the sent text to remain in clipboard
 * @param untilRevert {Integer} Time in milliseconds before clipboard is restored (default: 300)
 *                             Increase this value for slower applications that need more time
 *                             to process the paste operation
 * 
 * @example
 * ; Send a simple message with a space at the end
 * ClipSend("Hello World", " ")
 * 
 * @example  
 * ; Send multi-line text without reverting clipboard
 * ClipSend("Line 1`nLine 2`nLine 3", "", false)
 * 
 * @example
 * ; Send text to a slow application (like Discord) with longer revert time
 * ClipSend("Message for slow app", "", true, 500)
 */
ClipSend(toSend, endChar := "", isClipReverted := true, untilRevert := 300) {
	/**
	 * TIMING EXPLANATION:
	 * We cannot immediately revert the clipboard because there's no reliable way 
	 * to detect when an application has finished processing the paste command.
	 * 
	 * If we reverted the clipboard immediately, the application might receive
	 * the old clipboard content instead of our intended text, because the 
	 * revert happened before the application finished processing Ctrl+V.
	 * 
	 * The timing depends on the target application:
	 * - Fast apps (Notepad, VS Code): 50-100ms is usually sufficient
	 * - Medium apps (browsers, Office): 200-300ms recommended  
	 * - Slow apps (Discord, some games): 300-500ms may be needed
	 * 
	 * Adjust the untilRevert parameter based on your target application's speed.
	 */
	
	// Store the original clipboard content if we plan to restore it later
	if (isClipReverted) {
		prevClip := ClipboardAll()	// ClipboardAll() preserves all clipboard formats
	}

	// Clear the clipboard to ensure we start with a clean state
	A_Clipboard := ""
	
	// Set our desired content (text + optional ending character)
	A_Clipboard := toSend . endChar
	
	// Wait up to 1 second for the clipboard to be populated
	// This ensures the clipboard contains our text before we attempt to paste
	ClipWait(1)
	
	// Send the paste command using Shift+Insert instead of Ctrl+V
	// Shift+Insert is more universally supported and consistent across applications
	Send("{Shift Down}{Insert}{Shift Up}")

	// Schedule clipboard restoration if requested
	if (isClipReverted) {
		// Use a timer to restore the clipboard after the specified delay
		// The negative value (-untilRevert) makes this a one-time timer
		SetTimer(() => A_Clipboard := prevClip, -untilRevert)
		/**
		 * NOTE: This timer runs asynchronously, so ClipSend() returns immediately.
		 * The function doesn't block for the full untilRevert duration - only the
		 * clipboard restoration is delayed.
		 */
	}
}

; ===============================================================================
; GETINPUT - SIMPLIFIED INPUT CAPTURE
; ===============================================================================

/**
 * Simplified wrapper for AutoHotkey's InputHook functionality.
 * This function creates an input hook, starts it, waits for completion, and returns the result.
 * 
 * InputHook is AutoHotkey's method for capturing keyboard input without displaying it
 * on screen. This is useful for creating custom input dialogs, hotstring systems,
 * or capturing specific key sequences.
 * 
 * @param options {String} Optional InputHook options string
 *                        Common options:
 *                        - "L4" = Limit input to 4 characters
 *                        - "M" = Modify (allow backspace/delete)  
 *                        - "V" = Visible (show input as it's typed)
 *                        - "I" = Case insensitive
 *                        See AutoHotkey documentation for complete list
 * 
 * @param endKeys {String} Optional keys that will end the input capture
 *                        Examples: "{Enter}", "{Escape}", "{Tab}{Enter}"
 *                        If not specified, input continues until manually stopped
 * 
 * @returns {InputHook} The InputHook object containing the captured input and metadata
 *                     Access the input text with: result.Input
 *                     Access the ending key with: result.EndKey
 *                     Access the ending reason with: result.EndReason
 * 
 * @example
 * ; Capture up to 10 characters, ending with Enter or Escape
 * result := GetInput("L10", "{Enter}{Escape}")
 * if (result.EndKey = "Enter") {
 *     MsgBox("You entered: " . result.Input)
 * }
 * 
 * @example
 * ; Simple input capture with default settings
 * userInput := GetInput()
 * MsgBox("Captured: " . userInput.Input)
 */
GetInput(options?, endKeys?) {
	// Create the InputHook object with the specified options and end keys
	inputHookObject := InputHook(options?, endKeys?)
	
	// Start capturing input immediately
	inputHookObject.Start()
	
	// Block execution until input capture is complete
	// This happens when an end key is pressed or the hook is manually stopped
	inputHookObject.Wait()
	
	// Return the complete InputHook object so caller can access all properties
	return inputHookObject
}

; ===============================================================================
; GETCLICK - MOUSE CLICK CAPTURE AND COORDINATE RETRIEVAL  
; ===============================================================================

/**
 * Waits for a mouse click and returns the click coordinates along with window information.
 * This function blocks execution until the user performs a complete click action
 * (press and release of the left mouse button).
 * 
 * The function uses screen coordinates, meaning the returned coordinates are absolute
 * positions on the entire screen, not relative to any specific window.
 * 
 * Typical use cases:
 * - Getting coordinates for automation scripts
 * - Creating click-to-select interfaces  
 * - Capturing positions for image search references
 * - Building coordinate-based macros
 * 
 * @returns {Object} An object containing click information:
 *                  - x: X-coordinate of the click (screen coordinates)
 *                  - y: Y-coordinate of the click (screen coordinates)  
 *                  - window: Handle (HWND) of the window that was clicked
 * 
 * @example
 * ; Wait for user to click somewhere, then show the coordinates
 * MsgBox("Click anywhere on the screen...")
 * clickInfo := GetClick()
 * MsgBox("You clicked at: " . clickInfo.x . ", " . clickInfo.y)
 * 
 * @example
 * ; Capture click for automation script
 * ToolTip("Click the button you want to automate...")
 * target := GetClick()
 * ToolTip() ; Clear tooltip
 * ; Now you can use target.x and target.y for Click() commands
 * Click(target.x, target.y)
 */
GetClick() {
	// Set coordinate mode to screen to get absolute coordinates
	// This ensures coordinates are relative to the entire screen, not the active window
	CoordMode("Mouse", "Screen")
	
	// Wait for the left mouse button to be released (if it's currently pressed)
	// This prevents immediate triggering if the button is already down
	KeyWait("LButton", "U")
	
	// Wait for the left mouse button to be pressed down
	// This is where we detect the start of the click action
	KeyWait("LButton", "D")
	
	// Capture the mouse position and window information at the moment of click
	MouseGetPos(&x, &y, &window)

	// Return an object with all the captured information
	return {
		x: x,           // X-coordinate where the click occurred
		y: y,           // Y-coordinate where the click occurred  
		window: window  // Window handle (HWND) of the window that was clicked
	}
}
