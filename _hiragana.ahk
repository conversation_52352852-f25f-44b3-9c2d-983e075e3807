#Requires AutoHotkey v2.0
; Main script for Japanese IME handling

#Include ime.ahk
; #Include klid.ahk

; Initialize hook
hook := 0

; Set up persistent hook
SetWinEventHook()
OnExit(*) => UnhookWinEvent()

; Set up the event hook for window focus changes
SetWinEventHook() {
    global hook
    EVENT_SYSTEM_FOREGROUND := 0x0003
    EVENT_OBJECT_FOCUS := 0x8005
    
    hook := DllCall("User32\SetWinEventHook"
        , "Int", EVENT_SYSTEM_FOREGROUND
        , "Int", EVENT_OBJECT_FOCUS
        , "Ptr", 0
        , "Ptr", CallbackCreate(SetHiraganaCallback, "F")
        , "Int", 0
        , "Int", 0
        , "Int", 0)
}

; Callback function that runs when monitored events occur
SetHiraganaCallback(*) {
    global JA_KLID
    If (GetKlid() != JA_KLID)
        Return
    
    If (GetImeStatus() = 0)
        SetImeStatus(1)
}

; Clean up when exiting
UnhookWinEvent() {
    global hook
    If (hook)
        DllCall("User32\UnhookWinEvent", "Ptr", hook)
    ExitApp
}