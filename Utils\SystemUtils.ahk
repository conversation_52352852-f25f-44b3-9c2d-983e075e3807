; ===============================================================================
; SYSTEM UTILITIES
; ===============================================================================
; This file contains system-level utilities for command execution, timing control,
; and advanced input handling with screen position detection.
; 
; Classes and Functions included:
; - Cmd: Command line execution with output capture
; - Wait(): Non-blocking conditional waiting system
; - Press: Advanced hotkey and mouse position utilities
; ===============================================================================

; No external dependencies required for this module

; ===============================================================================
; CMD CLASS - COMMAND LINE EXECUTION AND OUTPUT CAPTURE
; ===============================================================================

/**
 * A comprehensive class for executing command line operations with full output capture.
 * This class creates a hidden console window, executes commands, and captures both
 * standard output and error streams.
 * 
 * Key features:
 * - Hidden console execution (no visible command prompt)
 * - Captures both stdout and stderr
 * - Supports command chaining with && operator
 * - Automatic console cleanup on object destruction
 * - Working directory control
 * 
 * The class uses Windows Script Host (WScript.Shell) for reliable command execution
 * and Windows API calls for console management.
 */
class Cmd {

	/**
	 * Creates a new Cmd instance with an associated hidden console window.
	 * 
	 * @param workingDir {String} The working directory for command execution
	 *                           Defaults to A_WorkingDir (current script directory)
	 * 
	 * @example
	 * ; Create a Cmd instance in the current directory
	 * cmd := Cmd()
	 * 
	 * @example  
	 * ; Create a Cmd instance in a specific directory
	 * cmd := Cmd("C:\MyProject")
	 */
	__New(workingDir := A_WorkingDir) {
		// Create a hidden console window and get its process ID
		PID := this._CreateConsoleWindow()
		
		// Attach our script to the console for API access
		this._AttachConsole(PID)
		
		// Create the WScript.Shell COM object for command execution
		this.shell := ComObject("WScript.Shell")
		
		// Set the working directory for all subsequent commands
		this.shell.CurrentDirectory := workingDir
	}

	/**
	 * Cleanup method called when the object is destroyed.
	 * Automatically detaches from the console to prevent resource leaks.
	 */
	__Delete() {
		this._FreeConsole()
	}

	/**
	 * Property that returns combined stdout and stderr output.
	 * This is a convenience property that tries stdout first, then stderr if stdout is empty.
	 * 
	 * @returns {String} The command output, preferring stdout over stderr
	 */
	StdOut => this.StdOutOnly || this.StdErr

	/**
	 * Property that returns only the standard output (stdout) from the last command.
	 * This excludes any error messages that might have been written to stderr.
	 * 
	 * @returns {String} The standard output, trimmed of whitespace
	 */
	StdOutOnly {
		get {
			output := this.exec.StdOut.ReadAll()
			return Trim(output, "`r`n`t ")
		}
	}

	/**
	 * Property that returns only the standard error (stderr) from the last command.
	 * This contains error messages and diagnostic information.
	 * 
	 * @returns {String} The standard error output, trimmed of whitespace
	 */
	StdErr {
		get {
			output := this.exec.StdErr.ReadAll()
			return Trim(output, "`r`n`t ")
		}
	}

	/**
	 * Executes one or more commands and waits for completion.
	 * Multiple commands are automatically chained with && operator.
	 * 
	 * @param commands {String...} One or more command strings to execute
	 *                            Multiple commands will be chained together
	 * 
	 * @returns {Cmd} Returns self for method chaining
	 * 
	 * @example
	 * ; Execute a single command
	 * cmd := Cmd().Execute("dir")
	 * MsgBox(cmd.StdOut)
	 * 
	 * @example
	 * ; Execute multiple chained commands  
	 * cmd := Cmd().Execute("cd C:\", "dir", "echo Done")
	 * MsgBox(cmd.StdOut)
	 */
	Execute(commands*) {
		// Convert the command array into a single command string with && separators
		commands := this._GetCommandString(commands)
		
		// Execute the command through cmd.exe with /C flag (execute and close)
		this.exec := this.shell.Exec(A_ComSpec " /C " commands)
		
		// Wait for the command to complete by polling the Status property
		// Status = 0 means still running, Status = 1 means completed
		While !this.exec.Status {
			Sleep(50)  // Small delay to prevent excessive CPU usage
		}
		
		return this  // Return self for method chaining
	}

	// Private property to store the execution object
	exec := unset

	/**
	 * Creates a hidden console window for command execution.
	 * This console is invisible to the user but allows us to capture output.
	 * 
	 * @returns {Integer} The process ID of the created console
	 * @private
	 */
	_CreateConsoleWindow() {
		// Enable detection of hidden windows for WinExist check
		DetectHiddenWindows(True)
		
		// Start a hidden command prompt process
		Run(A_ComSpec,, "Hide", &PID)
		
		// Wait for the console window to be created
		while !WinExist("ahk_pid " PID) {
			Sleep(50)
		}
		
		return PID
	}

	/**
	 * Converts an array of commands into a single command string.
	 * Commands are joined with && operator for sequential execution.
	 * 
	 * @param commands {Array} Array of command strings
	 * @returns {String} Single command string with && separators
	 * @private
	 */
	_GetCommandString(commands) {
		commandString := ""
		
		// Join all commands with && operator, except the last one
		for index, command in commands {
			if index = commands.Length {
				commandString .= command  // Last command doesn't need &&
				break
			}
			commandString .= command " && "
		}
		
		return commandString
	}

	/**
	 * Attaches the current process to the specified console.
	 * This allows us to capture the console's output streams.
	 * 
	 * @param PID {Integer} Process ID of the console to attach to
	 * @private
	 */
	_AttachConsole(PID) => DllCall("kernel32.dll\AttachConsole", "UInt", PID)

	/**
	 * Detaches from the current console.
	 * Called during cleanup to free system resources.
	 * 
	 * @private
	 */
	_FreeConsole() => DllCall("kernel32.dll\FreeConsole")

	/**
	 * Static utility method for running files with specific programs.
	 * This is syntactic sugar for the common pattern of "run file X with program Y".
	 *
	 * @param with {String} The program to use (either just the executable name or full path)
	 * @param runFile {String} The path to the file you want to open
	 *
	 * @example
	 * ; Open a text file with Notepad
	 * Cmd.RunWith("notepad.exe", "C:\myfile.txt")
	 *
	 * @example
	 * ; Open a video with VLC (assuming VLC is in PATH)
	 * Cmd.RunWith("vlc", "C:\video.mp4")
	 *
	 * @static
	 */
	static RunWith(with, runFile) => Run(with ' "' runFile '"')
}

; ===============================================================================
; WAIT FUNCTION - NON-BLOCKING CONDITIONAL WAITING
; ===============================================================================

/**
 * A sophisticated non-blocking wait function that executes actions based on conditions.
 * Unlike Sleep(), this function doesn't freeze the entire script - it uses timers to
 * periodically check conditions and execute actions when conditions are met.
 *
 * This is particularly useful for:
 * - Waiting for windows to appear/disappear
 * - Monitoring file system changes
 * - Waiting for network conditions
 * - Creating responsive automation that doesn't block user input
 *
 * The function uses AutoHotkey's SetTimer for non-blocking operation, meaning your
 * script remains responsive to hotkeys and other events while waiting.
 *
 * @param condition {Function} A function object that returns true/false
 *                            This function is called repeatedly until it returns true
 *                            Example: () => WinExist("Notepad")
 *
 * @param action {Function} A function object to execute when condition becomes true
 *                         This is called exactly once when the condition is satisfied
 *                         Example: () => MsgBox("Notepad appeared!")
 *
 * @param timeout {Integer} Maximum time to wait in milliseconds (default: 0 = no timeout)
 *                         If timeout is reached, the action is NOT executed
 *                         Set to 0 or negative for infinite waiting
 *
 * @param frequency {Integer} How often to check the condition in milliseconds (default: 1)
 *                           Lower values = more responsive but higher CPU usage
 *                           Higher values = less CPU usage but less responsive
 *                           Note: The parameter name "oftenity" was kept for compatibility
 *
 * @example
 * ; Wait for Notepad to appear, then activate it
 * Wait(
 *     () => WinExist("Notepad"),           ; Condition: check if Notepad exists
 *     () => WinActivate("Notepad"),        ; Action: activate when found
 *     5000,                                ; Timeout: give up after 5 seconds
 *     100                                  ; Frequency: check every 100ms
 * )
 *
 * @example
 * ; Wait for a file to be created (no timeout)
 * Wait(
 *     () => FileExist("C:\temp\signal.txt"),
 *     () => MsgBox("File created!"),
 *     0,    ; No timeout
 *     500   ; Check every half second
 * )
 *
 * @example
 * ; Wait for user to release a key
 * Wait(
 *     () => !GetKeyState("Ctrl", "P"),     ; Condition: Ctrl key not pressed
 *     () => ToolTip("Ctrl released!"),     ; Action: show tooltip
 *     10000,                               ; Timeout: 10 seconds max
 *     50                                   ; Check every 50ms for responsiveness
 * )
 */
Wait(condition, action, timeout := 0, oftenity := 1) {

	; Record the start time for timeout calculation
	startTime := A_TickCount

	; Internal function that gets called repeatedly by the timer.
	; This function checks the condition and handles timeout logic.
	Check() {
		; First check if we've exceeded the timeout (if timeout is set)
		if timeout > 0 && A_TickCount - startTime >= timeout {
			SetTimer(, 0)  ; Stop the timer - timeout reached without condition being met
			return
		}

		; Check if our condition is satisfied
		if !condition() {
			return  ; Condition not met yet, timer will call us again
		}

		; Condition is satisfied! Execute the action and stop the timer
		action()
		SetTimer(, 0)  ; Stop the timer - we're done
	}

	; Start the timer that will repeatedly call our Check function
	; The timer interval is set by the 'oftenity' parameter
	SetTimer(Check, oftenity)
}

; ===============================================================================
; PRESS CLASS - ADVANCED HOTKEY AND MOUSE POSITION UTILITIES
; ===============================================================================

/**
 * A comprehensive class for advanced input handling, including mouse position detection,
 * screen section analysis, and sophisticated hotkey processing with hold/tap detection.
 *
 * This class provides utilities for:
 * - Detecting which section of the screen the mouse cursor is in
 * - Executing different actions based on mouse position
 * - Distinguishing between key taps and holds
 * - Processing complex hotkey combinations
 *
 * The screen section detection is based on predefined coordinate ranges that divide
 * the screen into logical areas (corners, edges, center).
 */
class Press {

	/**
	 * Default duration in seconds that distinguishes a "tap" from a "hold".
	 * Keys pressed for less than this duration are considered taps.
	 * Keys held for longer are considered holds.
	 *
	 * @type {Float}
	 * @static
	 */
	static LongHoldDuration := 0.20

	/**
	 * Analyzes the current mouse position and determines which screen sections it occupies.
	 * The screen is divided into logical sections: edges, corners, and center.
	 *
	 * Screen section definitions (based on typical 1920x1080 resolution):
	 * - right: X > 1368 (right portion of screen)
	 * - left: X < 568 (left portion of screen)
	 * - down: Y > 747 (bottom portion of screen)
	 * - up: Y < 347 (top portion of screen)
	 * - corners: Specific combinations of edge positions
	 * - middle: Not in any edge section
	 *
	 * @returns {Object} Object with boolean properties for each section:
	 *                  - right, left, down, up: Edge sections
	 *                  - topRight, topLeft, bottomLeft, bottomRight: Corner sections
	 *                  - middle: True if not in any edge section
	 *
	 * @example
	 * ; Check where the mouse is and act accordingly
	 * sections := Press.GetSections()
	 * if (sections.topRight) {
	 *     MsgBox("Mouse is in top-right corner")
	 * } else if (sections.middle) {
	 *     MsgBox("Mouse is in center area")
	 * }
	 *
	 * @static
	 */
	static GetSections() {
		; Use screen coordinates for consistent positioning across all windows
		CoordMode("Mouse", "Screen")
		MouseGetPos(&sectionX, &sectionY)

		; Define edge sections based on screen coordinates
		right         := (sectionX > 1368)
		left          := (sectionX < 568)
		down          := (sectionY > 747)
		up            := (sectionY < 347)

		; Define corner sections as combinations of edges
		topRight      := ((sectionX > 1707) && (sectionY < 233))
		topLeft       := ((sectionX < 252) && (sectionY < 229))
		bottomLeft    := ((sectionX < 263) && (sectionY > 849))
		bottomRight   := ((sectionX > 1673) && (sectionY > 839))

		; Middle section is defined as not being in any edge
		middle        := !right && !left && !down && !up

		return {
			right:       right,
			left:        left,
			down:        down,
			up:          up,
			topRight:    topRight,
			topLeft:     topLeft,
			bottomLeft:  bottomLeft,
			bottomRight: bottomRight,
			middle:      middle
		}
	}

	/**
	 * Executes different actions based on whether the mouse is in a specific screen section.
	 * This is useful for creating context-sensitive hotkeys that behave differently
	 * depending on where the mouse cursor is located.
	 *
	 * @param whichSection {String} The name of the section to check
	 *                             Valid sections: "left", "right", "up", "down",
	 *                             "topLeft", "topRight", "bottomLeft", "bottomRight", "middle"
	 *
	 * @param ifSection {Function} Function to execute if mouse IS in the specified section
	 *                            Must be a function object (arrow function, bound function, etc.)
	 *
	 * @param ifNotSection {Function} Function to execute if mouse is NOT in the specified section
	 *                               Must be a function object (arrow function, bound function, etc.)
	 *
	 * @returns {Boolean} True if mouse was in the specified section, false otherwise
	 *
	 * @throws {ValueError} If whichSection is not a valid section name
	 * @throws {TypeError} If either function parameter is not a function object
	 *
	 * @example
	 * ; Different actions based on mouse position
	 * Press.ActOnSection("left",
	 *     () => MsgBox("Mouse on left side"),     ; Action if in left section
	 *     () => MsgBox("Mouse not on left side") ; Action if not in left section
	 * )
	 *
	 * @example
	 * ; Context-sensitive volume control
	 * Press.ActOnSection("right",
	 *     () => SoundSetVolume("+10"),  ; Increase volume if mouse on right
	 *     () => SoundSetVolume("-10")   ; Decrease volume if mouse elsewhere
	 * )
	 *
	 * @static
	 */
	static ActOnSection(whichSection, ifSection, ifNotSection) {
		sections := this.GetSections()

		; Validate that the requested section exists
		if !sections.HasProp(whichSection) {
			throw ValueError(Format('There is no section "{1}"', whichSection), -1,
				Format('Press.ActOnSection("{1}", ...)', whichSection))
		}

		; Validate function parameters
		static errorMessage := "Both ifSection and ifNotSection must be function objects or bound functions."

		ifSectionType := Type(ifSection)
		ifNotSectionType := Type(ifNotSection)

		; Get string representation for error messages (safely)
		try ifSectionString := String(ifSection)
		catch {
			ifSectionString := ""
		}

		if !(ifSectionType ~= "Func|BoundFunc") {
			throw TypeError(errorMessage "`nifSection is a " ifSectionType " instead", -1,
				Format('Press.ActOnSection(..., "{1}" , ...)', ifSectionString))
		}

		try ifNotSectionString := String(ifNotSection)
		catch {
			ifNotSectionString := ""
		}

		if !(ifNotSectionType ~= "Func|BoundFunc") {
			throw TypeError(errorMessage "`nifNotSection is a " ifNotSectionType " instead", -1,
				Format('Press.ActOnSection(..., ..., "{1}")', ifNotSectionString))
		}

		; Execute the appropriate function based on mouse position
		; Note: The logic appears inverted in the original, so we preserve that behavior
		if sections.%whichSection% {
			ifNotSection()
			return false
		} else {
			ifSection()
			return true
		}
	}

	/**
	 * Formats the current hotkey (A_ThisHotkey) into a format suitable for KeyWait.
	 * This function strips modifiers and combination operators to get the base key.
	 *
	 * Examples of transformations:
	 * - "CapsLock & f" becomes "f"
	 * - "+!d" (Shift+Alt+d) becomes "d"
	 * - "^j" (Ctrl+j) becomes "j"
	 * - "LWin & Space" becomes "Space"
	 *
	 * @returns {String} The base key name suitable for use with KeyWait
	 *
	 * @example
	 * ; In a hotkey function, get the base key for KeyWait
	 * ; If hotkey is "Ctrl+j", this returns "j"
	 * baseKey := Press.FormatThisHotkey()
	 * KeyWait(baseKey, "U")  ; Wait for the 'j' key to be released
	 *
	 * @static
	 */
	static FormatThisHotkey() {
		thisHotkey := A_ThisHotkey

		; First, handle combination hotkeys (e.g., "CapsLock & f")
		; Remove everything before " & " to get the second key
		thisHotkey := RegexReplace(thisHotkey, "^.* & ", "", &isAndedHotkey)

		; If it wasn't a combination hotkey, remove modifier symbols
		if !isAndedHotkey {
			; Remove modifier prefixes and suffixes:
			; # = Win key, ! = Alt, ^ = Ctrl, + = Shift
			; < > = Left/Right specific, * = Wildcard, ~ = Passthrough
			; $ = No SendInput, "up" suffix for key release
			thisHotkey := RegexReplace(thisHotkey, "[#!^+<>*~$]|(?i:[\t ]+up)", "")
		}

		return thisHotkey
	}

	/**
	 * Determines whether the current hotkey was held down or just tapped.
	 * This function waits for the key to be released and measures the duration.
	 *
	 * @param howLong {Float} Duration in seconds that distinguishes tap from hold
	 *                       Defaults to LongHoldDuration (0.20 seconds)
	 *                       Keys held longer than this are considered "holds"
	 *
	 * @returns {Boolean} True if the key was held (duration >= howLong)
	 *                   False if the key was tapped (duration < howLong)
	 *
	 * @example
	 * ; In a hotkey, do different actions for tap vs hold
	 * if (Press.Hold()) {
	 *     MsgBox("You held the key")
	 * } else {
	 *     MsgBox("You tapped the key")
	 * }
	 *
	 * @example
	 * ; Custom hold duration (0.5 seconds)
	 * if (Press.Hold(0.5)) {
	 *     MsgBox("Long hold detected")
	 * }
	 *
	 * @static
	 */
	static Hold(howLong := this.LongHoldDuration) => !KeyWait(this.FormatThisHotkey(), "U T" howLong)

	/**
	 * Convenience method that combines Hold() with immediate action execution.
	 * This is syntactic sugar for the common pattern of "do X on tap, Y on hold".
	 *
	 * @param tapFuncObj {Function} Function to execute if the key was tapped
	 * @param holdFuncObj {Function} Function to execute if the key was held
	 * @param howLong {Float} Duration threshold for tap vs hold (default: LongHoldDuration)
	 *
	 * @example
	 * ; Simple tap/hold actions in one line
	 * Press.Hold_Sugar(
	 *     () => Send("a"),           ; Tap action: send 'a'
	 *     () => Send("A")            ; Hold action: send 'A'
	 * )
	 *
	 * @example
	 * ; Volume control with custom timing
	 * Press.Hold_Sugar(
	 *     () => SoundSetVolume("+5"),    ; Tap: small volume increase
	 *     () => SoundSetVolume("+20"),   ; Hold: large volume increase
	 *     0.3                            ; Custom 0.3 second threshold
	 * )
	 *
	 * @static
	 */
	static Hold_Sugar(tapFuncObj, holdFuncObj, howLong := this.LongHoldDuration) {
		if KeyWait(this.FormatThisHotkey(), "U T" howLong) {
			tapFuncObj()   ; Key was released quickly (tap)
		} else {
			holdFuncObj()  ; Key was held down (hold)
		}
	}
}
