#Include <System\SoundPlayer>
#Include <Paths>

SoundPlayer.Storage.Set(

	"vine boom",         SoundPlayer(Paths.Ptf["vine boom"]),
	"heheheha",          SoundPlayer(Paths.Ptf["heheheha"]),
	"faded than a hoe",  SoundPlayer(Paths.Ptf["faded than a hoe"]),
	"shall we",          SoundPlayer(Paths.Ptf["shall we"]),
	"slip and crash",    SoundPlayer(Paths.Ptf["slip and crash"]),
	"cartoon running",   SoundPlayer(Paths.Ptf["cartoon running"]),
	"rizz",              SoundPlayer(Paths.Ptf["rizz"]),
	"bruh sound effect", SoundPlayer(Paths.Ptf["bruh sound effect"]),
	"cartoon",           SoundPlayer(Paths.Ptf["cartoon"]),
	"hohoho",            SoundPlayer(Paths.Ptf["hohoho"]),
	"bing chilling 1",   SoundPlayer(Paths.Ptf["bing chilling 1"]),
	"bing chilling 2",   SoundPlayer(Paths.Ptf["bing chilling 2"]),
	"oh fr on god",      SoundPlayer(Paths.Ptf["oh fr on god"]),
	"sus",               SoundPlayer(Paths.Ptf["sus"]),
	"i just farted",     SoundPlayer(Paths.Ptf["i just farted"]),
	"ting",              SoundPlayer(Paths.Ptf["ting"]),
	"shutter",           SoundPlayer(Paths.Ptf["shutter"]),
	"was that his cock", SoundPlayer(Paths.Ptf["was that his cock"]),
	"cyberpunk",         SoundPlayer(Paths.Ptf["cyberpunk"]),
	"better call saul",  SoundPlayer(Paths.Ptf["better call saul"]),

)