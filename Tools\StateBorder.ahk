#Include <Extensions\Gui>

class StateBorder {

	static __Item[stateName] {
		get {
			; If stateName is a number, return a numbered bulb
			if IsInteger(stateName) {
				; Initialize bulbs if needed
				if !StateBorder.Bulbs.Has(stateName)
					StateBorder.Bulbs[stateName] := StateBorder(StateBorder.DefaultThickness, StateBorder.DefaultColor)
				return StateBorder.Bulbs[stateName]
			}
			; Otherwise return named bulb
			return StateBorder.Bulbs.Has(stateName) ? StateBorder.Bulbs[stateName] : StateBorder.Bulbs["default"]
		}
	}

	__New(thickness := 10, color := 0xDE4D37) {
		this.Thickness := thickness
		this.Color := color
		; Initialize the four border GUIs (top, right, bottom, left)
		this.TopGUI := unset
		this.RightGUI := unset
		this.BottomGUI := unset
		this.LeftGUI := unset
		this.GuiExist := false
	}

	static Unit := A_ScreenDPI / 96
	static DefaultThickness := StateBorder.Unit * 10 ; Default border thickness
	static DefaultColor := 0xDE4D37 ; Default red color

	static Bulbs := Map(
		"default", StateBorder(StateBorder.DefaultThickness, StateBorder.DefaultColor)
	)

	Thickness := unset
	Color := unset
	GuiExist := false

	Toggle() {
		if this.GuiExist
			this.Destroy()
		else
			this.Create()
	}

	Create() {
		if this.GuiExist
			return

		; Get screen dimensions
		screenWidth := A_ScreenWidth
		screenHeight := A_ScreenHeight
		thickness := Round(this.Thickness)

		; Create top border
		this.TopGUI := Gui("AlwaysOnTop -Caption +ToolWindow")
		this.TopGUI.BackColor := this.Color
		this.TopGUI.Show(Format("NA w{1} h{2} x{3} y{4}", 
			screenWidth, thickness, 0, 0))
		this.TopGUI.NeverFocusWindow()
		this.TopGUI.MakeClickthrough()

		; Create right border
		this.RightGUI := Gui("AlwaysOnTop -Caption +ToolWindow")
		this.RightGUI.BackColor := this.Color
		this.RightGUI.Show(Format("NA w{1} h{2} x{3} y{4}", 
			thickness, screenHeight, screenWidth - thickness, 0))
		this.RightGUI.NeverFocusWindow()
		this.RightGUI.MakeClickthrough()

		; Create bottom border
		this.BottomGUI := Gui("AlwaysOnTop -Caption +ToolWindow")
		this.BottomGUI.BackColor := this.Color
		this.BottomGUI.Show(Format("NA w{1} h{2} x{3} y{4}", 
			screenWidth, thickness, 0, screenHeight - thickness))
		this.BottomGUI.NeverFocusWindow()
		this.BottomGUI.MakeClickthrough()

		; Create left border
		this.LeftGUI := Gui("AlwaysOnTop -Caption +ToolWindow")
		this.LeftGUI.BackColor := this.Color
		this.LeftGUI.Show(Format("NA w{1} h{2} x{3} y{4}", 
			thickness, screenHeight, 0, 0))
		this.LeftGUI.NeverFocusWindow()
		this.LeftGUI.MakeClickthrough()

		this.GuiExist := true
	}

	Destroy() {
		if !this.GuiExist
			return
			
		this.TopGUI.Destroy()
		this.RightGUI.Destroy()
		this.BottomGUI.Destroy()
		this.LeftGUI.Destroy()
		
		this.GuiExist := false
	}

	; Method to change border thickness
	SetThickness(newThickness) {
		this.Thickness := newThickness
		if this.GuiExist {
			; Recreate with new thickness
			this.Destroy()
			this.Create()
		}
	}

	; Method to change border color
	SetColor(newColor) {
		this.Color := newColor
		if this.GuiExist {
			; Recreate with new color
			this.Destroy()
			this.Create()
		}
	}

	; Static method to create a predefined bulb
	static CreateBorder(thickness := 0) {
		if (thickness = 0)  ; Use default thickness if 0 was passed
			thickness := StateBorder.DefaultThickness
			
		bulb := StateBorder(thickness, StateBorder.DefaultColor)
		bulb.Create()
		return bulb
	}

	; Initialize numbered bulbs for compatibility with existing code
	static InitializeBulbs(count := 10) {
		loop count {
			if !StateBorder.Bulbs.Has(A_Index)
				StateBorder.Bulbs[A_Index] := StateBorder(StateBorder.DefaultThickness, StateBorder.DefaultColor)
		}
	}
}

; Initialize numbered bulbs on script load
StateBorder.InitializeBulbs()