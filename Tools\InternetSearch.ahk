; ======================================================================================================================
; InternetSearch Class: Streamlined Web Searching
; ======================================================================================================================
;
; This class provides a flexible and efficient way to perform web searches using various search engines.
; It integrates with `CleanInputBox` to allow users to type their search queries and supports dynamic
; selection of search engines through a simple chording mechanism.
;
; How it works:
; 1. **Initialization**: When an `InternetSearch` object is created, it can be initialized with a default
;    search engine (e.g., "Google").
; 2. **Query Input**: The `TriggerSearch()` method prompts the user for a search query using `CleanInputBox`.
; 3. **Dynamic Engine Selection (Chording)**: Users can prepend their query with a short nickname
;    (e.g., "g " for Google, "y " for YouTube) to dynamically select a different search engine for that query.
;    If no nickname is provided, the default search engine (set during initialization) is used.
; 4. **Query Sanitization**: The `SanitizeQuery()` method ensures that the search query is properly URL-encoded
;    to handle special characters.
; 5. **Execution**: The sanitized query is then appended to the selected search engine's URL, and the
;    `Browser.RunLink()` method is called to open the search results in the default browser.
;
; Key Features:
; - **Predefined Search Engines**: A map of commonly used search engines with their base URLs.
; - **Search Engine Nicknames**: Short, memorable aliases for quick engine selection.
; - **Input Handling**: Leverages `CleanInputBox` for clean and robust user input.
; - **URL Encoding**: Automatically handles special characters in search queries.
;
; Example Hotkey Implementation (using chording):
; ```autohotkey
; #k:: {
;     ; Creates an InternetSearch object with Google as the default search engine.
;     ; If the user types "y my search term", it will search YouTube.
;     ; If the user types "my search term", it will search Google.
;     InternetSearch("Google").TriggerSearch()
; }
;
; ; More specific hotkeys for direct engine access:
; #+g::InternetSearch("Google").TriggerSearch()    ; Win+Shift+G for Google
; #+y::InternetSearch("Youtube").TriggerSearch()   ; Win+Shift+Y for YouTube
; #+e::InternetSearch("Emoji").TriggerSearch()     ; Win+Shift+E for Emojipedia
; ```
;
; ======================================================================================================================
#Include <Converters\Number>
#Include <Tools\CleanInputBox>
#Include <Extensions\String>
#Include <App\Browser>

; Inherits from CleanInputBox to leverage its input handling capabilities.
class InternetSearch extends CleanInputBox {


  ; Constructor: Initializes the InternetSearch object with a specified default search engine.
  ; @param searchEngine - The name of the default search engine (e.g., "Google").
  __New(searchEngine) {
    super.__New()
    ; Sets the base URL for the selected search engine.
    this.SelectedSearchEngine := this.AvailableSearchEngines[searchEngine]
  }

  ; Feeds the sanitized query to the browser to perform the search.
  ; @param input - The search query string.
  FeedQuery(input) {
    ; Sanitizes the query to ensure it's URL-safe.
    restOfLink := this.SanitizeQuery(input)
    ; Opens the search results in the browser.
    Browser.RunLink(this.SelectedSearchEngine restOfLink)
  }

  ; Dynamically reselects the search engine based on a nickname prepended to the input.
  ; @param input - The raw input string from the user.
  ; @returns The input string with the nickname removed, if a match was found.
  DynamicallyReselectEngine(input) {
    ; Iterates through defined nicknames to find a match.
    for key, value in this.SearchEngineNicknames {
      ; Checks if the input starts with a recognized nickname followed by a space.
      if input.RegExMatch("^" key " ") {
        ; If a match is found, update the selected search engine.
        this.SelectedSearchEngine := value
        ; Remove the nickname and the trailing space from the input.
        input := input[3, -1]
        break
      }
    }
    return input
  }

  ; Triggers the search process: gets input, reselects engine, and performs the search.
  TriggerSearch() {
    ; Waits for user input. If input is empty, aborts.
    if !input := super.WaitForInput() {
      return false
    }
    ; Dynamically selects the search engine and gets the cleaned query.
    query := this.DynamicallyReselectEngine(input)
    ; Performs the search.
    this.FeedQuery(query)
  }

  ; A map of available search engines with their base URLs.
  AvailableSearchEngines := Map(
    "Google",  "https://www.google.com/search?q=",
    "Youtube", "https://www.youtube.com/results?search_query=",
    "Emoji",   "https://emojipedia.org/search/?q=",
    "Yandex",  "https://yandex.ru/search/?text=",
    "Movies",  "https://watchmovieshd.ru/search?keyword=",
    "Phind",   "https://www.phind.com/search?q=",
    "Crates",  "https://crates.io/search?q=",
    "Rust Docs", "https://doc.rust-lang.org/stable/std/path/struct.PathBuf.html?search=",
  )

  ; A map of short nicknames for quick search engine selection.
  SearchEngineNicknames := Map(
    "g",  this.AvailableSearchEngines["Google"],
    "y",  this.AvailableSearchEngines["Youtube"],
    "e",  this.AvailableSearchEngines["Emoji"],
    "ya", this.AvailableSearchEngines["Yandex"],
    "m",  this.AvailableSearchEngines["Movies"],
    "p",  this.AvailableSearchEngines["Phind"],
    "c",  this.AvailableSearchEngines["Crates"],
    "r",  this.AvailableSearchEngines["Rust Docs"]
  )

	;Rename suggestion by @Micha-ohne-el, used to be ConvertToLink()
	SanitizeQuery(query) {
		SpecialCharacters := '%$&+,/:;=?@ "<>#{}|\^~[]``'.Split()
		for key, value in SpecialCharacters {
			query := query.Replace(value, "%" NumberConverter.DecToHex(Ord(value), false))
		}
		return query
	}
}
