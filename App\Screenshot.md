## Screenshot.ahk: Enhanced Screenshot Functionality

The `App/Screenshot.ahk` script enhances the default Windows screenshot functionality by providing programmatic control over the "Snip & Sketch" tool (or "Snipping Tool" in older Windows versions) and offering more granular control over the screenshot process.

### Additional Functionality Compared to Default Windows Screenshot

The default Windows screenshot tools typically rely on keyboard shortcuts:
*   `Win + Shift + S`: Opens the "Snip & Sketch" overlay, allowing you to select rectangular, free-form, window, or fullscreen snips.
*   `Print Screen`: Captures the entire screen to the clipboard.
*   `Alt + Print Screen`: Captures the active window to the clipboard.

`App/Screenshot.ahk` provides the following enhancements:

1.  **Programmatic Control over Snip & Sketch Modes:**
    *   While `Win + Shift + S` (mapped to `Screenshot.Start()`) opens the snipping tool, this script allows you to *programmatically select the snipping mode* (Rectangle, Free-form, Window, Fullscreen) without manual mouse clicks. This is achieved through its `UIA` (UI Automation) class, which interacts directly with the Snip & Sketch application's UI elements.
    *   `Screenshot.Rectangle()`: Selects the rectangular snip mode.
    *   `Screenshot.FreeForm()`: Selects the free-form snip mode.
    *   `Screenshot.Window()`: Selects the window snip mode.
    *   `Screenshot.Fullscreen()`: Selects the fullscreen snip mode.
    This is a significant advantage for automation, as you can trigger a specific snip type directly from a hotkey or another script.

2.  **Encapsulation and Reusability:**
    *   It encapsulates the logic for triggering various screenshot types into a reusable class, making it easier to integrate screenshot functionality into other AutoHotkey scripts.

### Where and How `Screenshot` Methods are Used

The `Screenshot` methods are integrated into your hotkey system across several files:

1.  **`Scr/App/Screenshot.ahk` (Internal Hotkeys):**
    This file itself contains hotkeys that directly control the Snip & Sketch modes using the UIA functionality:
    *   `WheelUp::Screenshot.Window()`
    *   `WheelDown::Screenshot.Rectangle()`
    *   `MButton::Screenshot.Fullscreen()`
    *   `z::Screenshot.Rectangle()`
    *   `x::Screenshot.FreeForm()`
    *   `c::Screenshot.Window()`
    *   `v::Screenshot.Fullscreen()`
    These hotkeys provide quick access to specific snipping modes once the Snip & Sketch tool is active.

2.  **`Scr/Keys/Hotkeys.ahk` (Main Hotkey File - Commented Out):**
    Your main `Hotkeys.ahk` file contains commented-out lines that show an intention to use these methods directly:
    *   `; <#w::Screenshot.CaptureWindow()`: Would capture the active window.
    *   `; <#e::Screenshot.CaptureScreen()`: Would capture the entire screen.
    *   `; PrintScreen::Screenshot.Start()`: Would initiate the Snip & Sketch tool.
    While currently inactive, these lines demonstrate how the `Screenshot` class was designed to be a central point for screenshot hotkeys.

3.  **`Scr/Keys/Numpad.ahk`:**
    This script actively uses the `Screenshot` methods, mapping them to Numpad hotkeys:
    *   `NumpadHome::Screenshot.Start()`: Starts the Snip & Sketch tool.
    *   `!NumpadHome::Screenshot.CaptureWindow()`: Captures the active window.
    *   `+NumpadHome::Screenshot.CaptureScreen()`: Captures the entire screen.
    This provides a dedicated set of screenshot hotkeys on the Numpad.

4.  **`Scr/Keys/VimMode.ahk`:**
    The `VimMode.ahk` script also integrates screenshot functionality:
    *   `Screenshot.CaptureScreen()`: Used within Vim mode to capture the entire screen.
    *   `Screenshot.CaptureWindow()`: Used within Vim mode to capture the active window.
    This indicates that screenshot actions are available as part of your "Vim mode" workflow.

In summary, `App/Screenshot.ahk` acts as a central module for managing and extending screenshot capabilities, allowing for both direct hotkey activation and programmatic control over the Windows Snip & Sketch tool, enhancing automation possibilities within your AutoHotkey environment.
