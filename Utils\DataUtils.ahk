; ===============================================================================
; DATA UTILITIES
; ===============================================================================
; This file contains utilities for data processing, file operations, and user
; interaction. These functions help with file management and creating interactive
; selection interfaces.
; 
; Functions included:
; - GetFilesSortedByDate(): Retrieve and sort files by modification time
; - Choose(): Interactive option selection using Info dialogs
; ===============================================================================

; External dependencies
#Include <Abstractions\GetFileTimes>  ; Required for file time retrieval
#Include <Tools\Info>                ; Required for Choose() dialog system

; ===============================================================================
; FILE SORTING BY DATE - CHRONOLOGICAL FILE ORGANIZATION
; ===============================================================================

/**
 * Retrieves files matching a pattern and sorts them by modification date.
 * This function is particularly useful for processing files in chronological order,
 * such as log files, backups, or any time-sensitive file operations.
 * 
 * The function uses AutoHotkey's file loop to find matching files, then sorts them
 * based on their last modification timestamp. You can choose to sort from newest
 * to oldest (default) or oldest to newest.
 * 
 * @param pattern {String} File pattern to search for (supports wildcards)
 *                        Examples: "*.txt", "C:\logs\*.log", "backup_*.zip"
 *                        Follows standard AutoHotkey file pattern syntax
 * 
 * @param newToOld {Boolean} Sort order for the results (default: true)
 *                          true = Newest files first (descending chronological order)
 *                          false = Oldest files first (ascending chronological order)
 * 
 * @returns {Array} Array of full file paths sorted by modification date
 *                 Each element is a complete file path string
 *                 Empty array if no files match the pattern
 * 
 * @example
 * ; Get all text files in current directory, newest first
 * files := GetFilesSortedByDate("*.txt")
 * for index, filePath in files {
 *     MsgBox("File " . index . ": " . filePath)
 * }
 * 
 * @example
 * ; Get log files from specific directory, oldest first
 * logFiles := GetFilesSortedByDate("C:\logs\*.log", false)
 * if (logFiles.Length > 0) {
 *     MsgBox("Oldest log file: " . logFiles[1])
 * }
 * 
 * @example
 * ; Process backup files in chronological order
 * backups := GetFilesSortedByDate("D:\backups\backup_*.zip", false)
 * for index, backup in backups {
 *     ; Process backups from oldest to newest
 *     ProcessBackupFile(backup)
 * }
 */
GetFilesSortedByDate(pattern, newToOld := true) {
	; Use a Map to store files with their modification times as keys
	; This allows for automatic sorting when we iterate through the map
	files := Map()
	
	; Loop through all files matching the specified pattern
	loop files pattern {
		; Get the modification time for the current file
		; GetFileTimes returns an object with various timestamp properties
		modificationTime := GetFileTimes(A_LoopFileFullPath).ModificationTime
		
		; If we want newest files first, negate the timestamp
		; This reverses the sort order since Map iteration is typically ascending
		if (newToOld) {
			modificationTime *= -1
		}
		
		; Store the file path with the (possibly negated) timestamp as the key
		; Map keys are automatically sorted, so this gives us chronological ordering
		files.Set(modificationTime, A_LoopFileFullPath)
	}
	
	; Convert the Map values to an Array for easier handling
	; The Map iteration order gives us the files in the desired chronological sequence
	arr := []
	for timestamp, fullPath in files {
		arr.Push(fullPath)
	}
	
	return arr
}

; ===============================================================================
; CHOOSE FUNCTION - INTERACTIVE OPTION SELECTION
; ===============================================================================

/**
 * Creates an interactive selection interface using multiple Info dialog windows.
 * This function displays each option in a separate Info window and waits for the
 * user to close one of them, returning the text of the selected option.
 * 
 * The function leverages the Info dialog system to create a visual selection menu
 * where each option appears as a separate window. The user selects an option by
 * closing its corresponding window, and the function returns the text of that option.
 * 
 * This is useful for creating quick selection menus, confirmation dialogs with
 * multiple choices, or any scenario where you need the user to pick from a list
 * of predefined options.
 * 
 * Technical details:
 * - Creates one Info window per option (up to the maximum allowed by the Info system)
 * - Continuously monitors which windows exist to detect user selection
 * - Automatically cleans up all remaining windows after selection
 * - Returns the text content of the selected option
 * 
 * @param options {String...} Variable number of option strings to display
 *                           Each parameter becomes a selectable option
 *                           Limited by Infos.maximumInfos (defined in Info system)
 * 
 * @returns {String} The text of the option that was selected by the user
 *                  Returns the exact string that was passed as a parameter
 * 
 * @example
 * ; Simple yes/no choice
 * choice := Choose("Yes", "No", "Cancel")
 * MsgBox("You chose: " . choice)
 * 
 * @example
 * ; File operation menu
 * action := Choose("Copy File", "Move File", "Delete File", "Rename File")
 * switch action {
 *     case "Copy File":
 *         ; Perform copy operation
 *     case "Move File":
 *         ; Perform move operation
 *     ; ... etc
 * }
 * 
 * @example
 * ; Configuration selection
 * config := Choose("Development", "Testing", "Production")
 * LoadConfiguration(config)
 */
Choose(options*) {
	; Create an array to store Info dialog objects
	; Start with one empty Info to ensure proper initialization
	infoObjs := [Infos("")]
	
	; Create Info windows for each option
	for index, option in options {
		; Respect the maximum number of Info windows allowed by the system
		if infoObjs.Length >= Infos.maximumInfos {
			break  ; Stop creating windows if we've reached the limit
		}
		
		; Create a new Info window displaying this option
		infoObjs.Push(Infos(option))
	}
	
	; Monitor the Info windows to detect when one is closed (selected)
	loop {
		; Check each Info window to see if it still exists
		for index, infoObj in infoObjs {
			; If a window no longer exists, the user has selected that option
			if !WinExist(infoObj.hwnd) {
				; Store the text of the selected option
				text := infoObj.text
				break 2  ; Break out of both the for loop and the outer loop
			}
		}
	}
	
	; Clean up: destroy all remaining Info windows
	for index, infoObj in infoObjs {
		infoObj.Destroy()
	}
	
	; Return the text of the selected option
	return text
}
