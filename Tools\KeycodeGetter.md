# `Tools\KeycodeGetter.ahk` - Key Code Getter Utility

This AutoHotkey script provides a graphical user interface (GUI) that allows users to input a key (e.g., by pressing it or typing its name) and then displays its corresponding key name, scan code (SC), and virtual key code (VK). It's a useful tool for AutoHotkey developers to quickly identify key codes for scripting purposes.

## Functionalities

The `KeyCodeGetter()` function creates and manages a GUI window for displaying key information.

### Function: `KeyCodeGetter()`

-   **Purpose**: Creates and manages a GUI window to display key codes.
-   **Behavior**:
    -   **<PERSON>ton Pattern**: It ensures that only one instance of the `KeyCodeGetter` GUI is active at a time. If the window already exists, it simply minimizes/restores it.
    -   **GUI Creation**: If the GUI doesn't exist, it creates a new window with a dark mode theme and a larger font.
    -   **Input Field**: An edit control (`g_values_input`) is provided for the user to type or press a key.
    -   **Display Fields**: Three text controls (`g_values_name`, `g_values_SC`, `g_values_VK`) are used to display the key's name, scan code, and virtual key code, respectively.
    -   **Hotkeys**: Sets up global hotkeys (`Enter` to submit, `Escape` to close) and context-sensitive hotkeys (`F1`, `F2`, `F3` to copy the displayed values to the clipboard).
    -   **Click-to-Copy**: The displayed key name, SC code, and VK code can also be copied to the clipboard by clicking on their respective text fields.
    -   **Code Conversion**: It uses `GetKeyName()`, `GetKeySC()`, and `GetKeyVK()` to retrieve the key information. Notably, it formats the SC and VK codes into hexadecimal representation, as they are typically represented this way in AutoHotkey scripting.
    -   **Destruction**: The `Destruction()` function handles the cleanup when the GUI is closed, disabling the associated hotkeys and destroying the GUI object.

## Dependencies

The `KeycodeGetter.ahk` script has the following dependencies, included via `#Include` directives:

-   `Utils\Win.ahk`: Used for window management, specifically for minimizing/restoring the `KeyCodeGetter` GUI (`Win({winTitle: values_hwnd}).MinMax()`).
-   `Extensions\Gui.ahk`: Provides extended GUI functionalities, which are used for creating and styling the `KeyCodeGetter` window (e.g., `DarkMode()`, `MakeFontNicer()`).

## Possible Use Cases and Hotkey Demonstrations

`KeycodeGetter.ahk` is primarily a developer tool, useful for debugging and creating AutoHotkey scripts that rely on specific key codes.

### Use Case 1: Quickly Get Key Codes for Scripting

A common scenario is when you need to know the exact SC or VK code for a non-standard key or a key combination to use in a hotkey definition or `Send` command.

```autohotkey
; Example Hotkey: Ctrl + Shift + K (to open the Key Code Getter)
^!k::
{
    KeyCodeGetter() ; Call the function to open the GUI
}
return
```

**Explanation**: When `Ctrl + Shift + K` is pressed, the `KeyCodeGetter` GUI will appear. You can then press any key (or type its name) in the input field, press `Enter`, and see its corresponding key name, SC, and VK codes. You can then use `F1`, `F2`, or `F3` (or click the text) to copy the desired code to your clipboard for use in your script.

### Use Case 2: Debugging Hotkey Conflicts

If a hotkey isn't behaving as expected, you can use the `KeyCodeGetter` to verify what AutoHotkey is registering when you press that key.

```autohotkey
; No specific hotkey needed for this use case, as it's more about observation.
; You would trigger the KeyCodeGetter GUI (e.g., with the hotkey from Use Case 1),
; then press the problematic key to see its codes.
```

**Explanation**: By observing the output in the `KeyCodeGetter` GUI, you can confirm if AutoHotkey is correctly identifying the key press and its associated codes, which can help in troubleshooting hotkey conflicts or incorrect key definitions.

## How Other Files Use This AHK

As identified by the search, `Tools\KeycodeGetter.ahk` is used by `Scr\GeneralKeyChorder_commented.ahk` and `Scr\Keys\Hotkeys.ahk`.

**`Scr\GeneralKeyChorder_commented.ahk`**

```autohotkey
#Include <Tools\KeycodeGetter>

; ... other code ...

; Example usage within a chording definition:
; This line suggests that 'k' followed by another key might trigger the KeyCodeGetter
"k", KeyCodeGetter, ; Runs the KeyCodeGetter tool

; ... other code ...
```

This indicates that `KeyCodeGetter` can be integrated into a chording system, allowing users to trigger the key code getter by pressing a sequence of keys (e.g., a prefix key followed by 'k').

**`Scr\Keys\Hotkeys.ahk`**

```autohotkey
#Include <Tools\KeycodeGetter>

; ... other code ...
```

In `Scr\Keys\Hotkeys.ahk`, the `#Include` directive makes the `KeyCodeGetter()` function available. While the provided snippet doesn't show a direct hotkey assignment, it implies that `KeyCodeGetter()` is intended to be called by a hotkey or another function within that script, similar to the example in Use Case 1.

In summary, `KeyCodeGetter.ahk` is a utility function designed to be called on demand, typically via a hotkey, to assist in AutoHotkey script development and debugging by providing quick access to key code information.
