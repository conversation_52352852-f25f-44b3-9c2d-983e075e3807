#Include <Extensions\Map>
#Include <Tools\StateBorder>
#Include <ime>

class Language {
	; if Japanese is on and for some reason reload the script 
	; if we did nothing it wouldn't update the state bulb
	; this static new method checks the language and if it's Japanese creates the state bulb back 
	static __New() {
		if this._current = this.LangToCode["Japanese"]
			StateBorder[this.BulbIndex].Create()
	}

	static BulbIndex := 3
	; track what language we're using by updating this variable
	; every time  we change a language with "windows + space"
	static _current := this._GetCurrentLanguageCode()

	static Current {
		get => this._current
		set {
			switch Type(value) {
				case "String":  code := this.LangToCode[value]
				case "Integer": code := value
				default:        throw ValueError("Wrong type passed.")
			}
			this._current := code
			this._ChangeLanguage(code)
		}
	}

	static CurrentWord {
		get => this.CodeToLang[this._current]
	}

	; use Infos(Language._GetCurrentLanguageCode()) to get the code of the current language
	static CodeToLang := Map(
		"0x4090409", "English", ; only the first code is necessary, the second one is for readability
		"0x4110411", "Japanese"
	)
	; reverse the map: language -> code
	; refer to Extensions/Map.ahk
	static LangToCode := this.CodeToLang.Reverse()

	static ToJapanese() {
		this.Current := "Japanese"
		StateBorder[this.BulbIndex].Create()
		
		; Give Windows time to update the keyboard layout <-- solved for most applications
		; Alternative IME Activation for Notepad needed
		; TODO simulating Alt+~ (the typical IME toggle shortcut) for Notepad
		SetTimer(() => this._TrySetHiragana(), -200)
	}

	static _TrySetHiragana() {
		global JA_KLID
		if (GetKlid() = JA_KLID && GetImeStatus() = 0)
			SetImeStatus(1)
	}

	static ToEnglish() {
		this.Current := "English"
		StateBorder[this.BulbIndex].Destroy()
	}

	static Toggle() {
		switch this.CurrentWord {
			case "Japanese": this.ToEnglish()
			case "English": this.ToJapanese()
		}
	}

 	; Returns the code of a language and runs only once when the script loads
	; instead of always calling this function 
	; use --> static _current := this._GetCurrentLanguageCode() and store it in a variable 
	static _GetCurrentLanguageCode() => "0x" Format("{:x}", dllCall("GetKeyboardLayout", "int", 0))

	; change the language 
	; it takes in a language code from the above function
	static _ChangeLanguage(languageCode) {
		; tries to post a message to the current window
		; works everywhere except other ahk GUIs
		; hotkey to toggle my language is only active if another hotkey window is not active
		; refer to funcional.ahk 
		try PostMessage(0x0050,, languageCode,, "A")
	}

}