#Requires AutoHotkey v2.0
; Minimal Language Border Indicator
; Shows a red border when Japanese IME is active

; Windows message constants for IME control
WM_IME_CONTROL := 0x0283
IMC_GETOPENSTATUS := 0x0005
IMC_SETOPENSTATUS := 0x0006

; Japanese keyboard layout ID
JA_KLID := 0x411

class BorderIndicator {
    static Thickness := A_ScreenDPI / 96 * 10
    static Color := 0xDE4D37  ; Red color
    static GuiExist := false
    static TopGUI := unset
    static RightGUI := unset
    static BottomGUI := unset
    static LeftGUI := unset
    
    ; Create border around screen
    static Create() {
        if this.GuiExist
            return
        
        ; Get screen dimensions
        screenWidth := A_ScreenWidth
        screenHeight := A_ScreenHeight
        thickness := Round(this.Thickness)
        
        ; Create top border
        this.TopGUI := Gui("AlwaysOnTop -Caption +ToolWindow")
        this.TopGUI.BackColor := this.Color
        this.TopGUI.Show(Format("NA w{1} h{2} x{3} y{4}", 
            screenWidth, thickness, 0, 0))
        this.TopGUI.Opt("+E0x20")  ; Clickthrough
        
        ; Create right border
        this.RightGUI := Gui("AlwaysOnTop -Caption +ToolWindow")
        this.RightGUI.BackColor := this.Color
        this.RightGUI.Show(Format("NA w{1} h{2} x{3} y{4}", 
            thickness, screenHeight, screenWidth - thickness, 0))
        this.RightGUI.Opt("+E0x20")  ; Clickthrough
        
        ; Create bottom border
        this.BottomGUI := Gui("AlwaysOnTop -Caption +ToolWindow")
        this.BottomGUI.BackColor := this.Color
        this.BottomGUI.Show(Format("NA w{1} h{2} x{3} y{4}", 
            screenWidth, thickness, 0, screenHeight - thickness))
        this.BottomGUI.Opt("+E0x20")  ; Clickthrough
        
        ; Create left border
        this.LeftGUI := Gui("AlwaysOnTop -Caption +ToolWindow")
        this.LeftGUI.BackColor := this.Color
        this.LeftGUI.Show(Format("NA w{1} h{2} x{3} y{4}", 
            thickness, screenHeight, 0, 0))
        this.LeftGUI.Opt("+E0x20")  ; Clickthrough
        
        this.GuiExist := true
    }
    
    ; Destroy all border GUIs
    static Destroy() {
        if !this.GuiExist
            return
            
        this.TopGUI.Destroy()
        this.RightGUI.Destroy()
        this.BottomGUI.Destroy()
        this.LeftGUI.Destroy()
        
        this.GuiExist := false
    }
}

class LanguageMonitor {
    static InitialCheck() {
        ; Check if Japanese is active on script start
        if this.IsJapaneseActive()
            this.HandleJapaneseActivation()
    }
    
    ; Check if Japanese keyboard is active
    static IsJapaneseActive() {
        return GetKlid() = JA_KLID
    }
    
    ; Handle Japanese keyboard activation
    static HandleJapaneseActivation() {
        BorderIndicator.Create()
        ; Try setting hiragana input mode
        SetTimer(() => this.TrySetHiragana(), -200)
    }
    
    ; Handle non-Japanese keyboard activation
    static HandleNonJapaneseActivation() {
        BorderIndicator.Destroy()
    }
    
    ; Try to set hiragana input mode for Japanese IME
    static TrySetHiragana() {
        if (GetKlid() = JA_KLID && GetImeStatus() = 0)
            SetImeStatus(1)
    }
    
    ; Monitor for language changes
    static MonitorLanguageChange() {
        if this.IsJapaneseActive()
            this.HandleJapaneseActivation()
        else
            this.HandleNonJapaneseActivation()
    }
}

; Gets the current IME status (0 = Direct Input "A", 1 = Japanese Input "あ")
GetImeStatus() {
    hwnd := WinExist("A")
    
    return DllCall("SendMessage"
        , "UInt", DllCall("imm32\ImmGetDefaultIMEWnd", "Uint", hwnd)
        , "UInt", WM_IME_CONTROL
        , "Int", IMC_GETOPENSTATUS
        , "Int", 0)
}

; Sets the IME status (0 = Direct Input "A", 1 = Japanese Input "あ")
SetImeStatus(ImeStatus) {
    hwnd := WinExist("A")
    
    return DllCall("SendMessage"
        , "UInt", DllCall("imm32\ImmGetDefaultIMEWnd", "Uint", hwnd)
        , "UInt", WM_IME_CONTROL
        , "Int", IMC_SETOPENSTATUS
        , "Int", ImeStatus)
}

; Gets the lower 16 bits from a 32-bit value
GetLowWord(Word) {
    return Word & (2 ** 16 - 1)
}

; Gets the keyboard layout ID of the active window
GetKlid() {
    WinId := WinExist("A")
    
    return GetLowWord(DllCall("GetKeyboardLayout"
        , "UInt", DllCall("GetWindowThreadProcessId", "UInt", WinId, "UInt", 0)
        , "UInt"))
}

; Register the Windows key + Space hotkey event
OnMessage(0x100, OnKeyDown)  ; WM_KEYDOWN

OnKeyDown(wParam, lParam, msg, hwnd) {
    static VK_SPACE := 0x20
    
    ; Check if Windows key is down and Space was pressed
    if (wParam = VK_SPACE && GetKeyState("LWin", "P")) {
        ; Need slight delay to allow Windows to complete the language switch
        SetTimer(MonitorLanguage, -300)
    }
}

MonitorLanguage() {
    LanguageMonitor.MonitorLanguageChange()
}

; Check language status on script start
LanguageMonitor.InitialCheck()