#Include <Abstractions\Mouse>
#Include <Abstractions\WindowManager>
#Include <Utils\InputOutput>
#Include <Tools\HoverScreenshot>
#Include <Extensions\String>
#Include <Tools\Info>
#Include <Tools\Counter>
#Include <Tools\CoordInfo>
#Include <Tools\KeycodeGetter>
#Include <Tools\WindowInfo>           ; #y::WindowInfo()
#Include <Tools\InternetSearch>
#Include <System\Brightness>
#Include <Abstractions\Registers>
#Include <Tools\RelativeCoordInfo>
#Include <System\Language>
#Include <Utils\SystemUtils>
#Include <Tools\Hider>
#Include <Abstractions\Base>
#Include <Abstractions\MediaActions>
#Include <Misc\CloseButActually>

; -------------------------------------------
; #Include <ime>
; #Include <klid>
; -------------------------------------------
#Include <System\System>
#Include <Abstractions\Script>
; -------------------------------------------

#Include <Utils\Win>
#Include <Environment>
#Include <Paths>
#Include <App\Browser>
#Include <App\VsCode>
#Include <App\Terminal>

#Include <OCR\ScreenSnipOCR>

; -------------------------------------------
#InputLevel 10  ; Use the highest priority level
#^!+::return ; Block any copilot activation

; XXX::{...} multi-line command block ; XXX::Run(...) for a single-line command
/**********************************
 * Shift + Mouse Wheel (Hold Shift for faster scrolling)
 ***********************************/
+WheelDown::(Send("{WheelDown 7}"))
+WheelUp::(Send("{WheelUp 7}"))

#InputLevel 6 ; have a higher input level than the input level 5
/**********************************
 * Win + Alt (LH + RH)
 ***********************************/

; remain functional even when the script is suspended
#SuspendExempt true ; allows hotkeys to work even when the script is suspended
#!y::Script.Suspend() ; with popup showing the status
#!-::Script.Reload()
; #!i::Script.Test()
; #!o::Script.ExitTest()
; #!p::Script.RunTests()
#!r::System.Reboot()
#!s::System.Sleep()
#!=::System.PowerDown()
; #!NumpadSub::System.PowerDownSafely()
; #!-::
#!v::
{
    ; Run("windowsdefender://history") ; Opens Protection History directly
    Run("explorer windowsdefender://history") ; Use 'explorer' to handle the protocol
}
#SuspendExempt false
/**********************************
 * Win + Alt (LH + RH)
 ***********************************/
;solved with regular win+num
; #!u::(Run("C:\zen\zen-dai\launcher\zendai.lnk")) ; win+7 when running
; #!i::(Run("C:\zen\zen-handsfree\launcher\zenhandsfree.lnk")) ; win+8 when running
; #!o::(Run("C:\zen\zen-ed\launcher\zened.lnk")) ; win+9 when running

; #Tab::Send("^!{Tab}")

/**********************************
 * Alt + Shift (RHthumb only)
 ***********************************/
; -------------------------------------------
#MaxThreadsBuffer true
; #HotIf WinExist(Browser.Chat.winTitle)
; !t::Browser.Chat.winObj.MinMax()
; #HotIf

!+b::Browser.winObj.App() ; alt+shift+b main franca zen browser
!+v::VsCode.winObj.App()
; !+c::Chrome.winObj.App()
!+x::Obsidian.winObj.App()

!+a::Terminal.winObj.App()
!+s::Sublime.winObj.App()
!+e::FilePilot.winObj.App()

; !+1::{...} with the braces for a multi-line command block
; !+1::Run(...) for a single-line command
; !+0::(Run("C:\zen\zen-01-portable\launcher\zen01.lnk"))
; !+1::(Run("C:\zen\zen-windows-portable\launcher\zen1.lnk"))

; !+4::(Run("C:\zen\zen-h\launcher\zenh.lnk"))
; !+5::(Run("C:\zen\zen-ai\launcher\zenai.lnk"))
; !+6::(Run("C:\zen\zen-ai\launcher\zenai.lnk"))
!+7::(Run("C:\zen\zen-rina\launcher\zenrina.lnk"))
!+8::(Run("C:\zen\zen-anzu\launcher\zenanzu.lnk"))
!+9::(Run("C:\zen\zen-windows-portable\launcher\zen1.lnk"))

/**********************************
 * Alt + Shift (RHthumb only) + RH -> windowsPad
 ***********************************/


#MaxThreadsBuffer false
; -------------------------------------------
; switch to glazeWM for killing the active window
!+w::Send("{Alt down}{F4}{Alt up}") ; close the active application ;not enough for sublime normal mode
!+[::WinMaximize("A") ; maximizes the active window: alt+shift+e
; !+q::return ; "return" disables the hotkey
!+]::WinRestore("A")
; !+h::Send("{LWin down}{Left}{LWin up}") ;invokes copilot

; !+a::SelectAll()
; !+s::MediaActions.SkipPrev() ; !+d::MediaActions.SkipNext()
; ; !+f::Send("{Browser_Back}") ; ; !+g::Send("{Browser_Forward}")
; !+z::Send("{F5}") ; !+x::Cut() ; !+v::Paste() ; !+c::Copy()

; <#w::Screenshot.CaptureWindow()
; <#e::Screenshot.CaptureScreen()

; ERROR:This value of type "String" has no method named "Show".
; <#f::HoverScreenshot().UseRecentScreenshot().Show()


#InputLevel 5

; ; !Tab::Explorer.winObj.MinMax()
; !Escape::GroupDeactivate("Main")
; <^Escape::CloseButActually()
; <+Escape::WinMinimize("A")
; ; >+Escape::SomeLockHint("CapsLock", 2)

; PrintScreen::Screenshot.Start()

; #HotIf directive: make a hotkey perform a different action
#HotIf !WinActive("ahk_exe " A_AhkPath)
#Space::Language.Toggle()
#HotIf


#^LButton::CoordInfo()
#+LButton::RelativeCoordInfo.BetterCallThis()
#y::WindowInfo()

; #9::try HoverScreenshot().SelectPath(Paths.Pictures).Show() ;works
#0::HoverScreenshot().UseRecentScreenshot().Show() ;error
#k::InternetSearch("Google").TriggerSearch()
; #w::Hider(false)
; #e:: {
; 	CoordMode("Mouse", "Screen")
; 	MouseGetPos(&x, &y)
; 	Point.Color := PixelGetColor(x, y)
; }

; #n::Registers(GetInput("L1", "{Esc}").Input).WriteOrAppend(CleanInputBox().WaitForInput().Replace("``n", "`n"))
; #m::Registers(GetInput("L1", "{Esc}").Input).WriteOrAppend()
; #!m::Registers(GetInput("L1", "{Esc}").Input).Paste()
; #^m::Registers(GetInput("L1", "{Esc}").Input).Paste().Truncate()
; #sc33::Registers(GetInput("L1", "{Esc}").Input).Run()
; #!sc33::Registers(GetInput("L1", "{Esc}").Input).Truncate()
; #sc28::Registers(GetInput("L1", "{Esc}").Input).Look()
; #!sc28::Registers.PeekNonEmpty()
; #!sc34::Registers(GetInput("L1", "{Esc}").Input).SwitchContents(GetInput("L1", "{Esc}").Input)
#c::ScreenSnipOCR.Capture()

; ====================================================================
; AHK Function to listen for Surfingkeys commands from the clipboard
; ====================================================================
;AHK2: scripts are persistent by default
OnClipboardChange(ClipboardListener)
Return

ClipboardListener(Type) {
  ; We are only interested when the clipboard contains text.
  if (Type = 1) { 
    ; AHK2: clipboard accessed through built-in variable A_Clipboard
    ; Check if the clipboard content is our exact trigger string.
    if (A_Clipboard = "sk-split-tab-trigger") {
      ; 1. IMPORTANT: Clear the clipboard immediately to prevent re-triggering.
      A_Clipboard := ""
      
      ; 2. Ensure the Zen Browser with the Browser.winTitle class
      if WinExist(Browser.winTitle) {
        WinActivate(Browser.winTitle)
        
        ; 3. Send the native split-tab hotkey.
        Send("^+f") ; Ctrl+Shift+F

        ; additional ctrl+tab to move focus to the newly created tab on the right
        Sleep(150)
        Send("^{Tab}")
      }
    }
  }
}
