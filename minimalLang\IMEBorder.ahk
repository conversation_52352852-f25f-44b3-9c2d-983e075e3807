#Requires AutoHotkey v2.0
; ======================================================================================
; IME Border Indicator 
; 
; This script creates a red border around the screen when Japanese IME is active.
; The border helps you quickly see which input mode (English or Japanese) is currently enabled.
; Press Win+Space to toggle between English and Japanese input modes.
; ======================================================================================

; -------------------------------------------------------------------------
; IME Control Functions
; These functions interact with Windows' Input Method Editor (IME) system
; -------------------------------------------------------------------------

; Windows message constants for controlling the IME
WM_IME_CONTROL := 0x0283       ; Windows message ID for IME control
IMC_GETOPENSTATUS := 0x0005    ; Command to check if IME is open (Japanese mode)
IMC_SETOPENSTATUS := 0x0006    ; Command to set IME open status
JA_KLID := 0x411               ; Japanese keyboard layout ID (hexadecimal)

; Gets whether IME is currently in Japanese input mode (あ) or direct input mode (A)
; Returns: 0 = Direct Input "A", 1 = Japanese Input "あ"
GetImeStatus() {
    ; Get handle to the active window
    hwnd := WinExist("A")
    
    ; Ask the IME system about its current status through a Windows message
    return DllCall("SendMessage"
        , "UInt", DllCall("imm32\ImmGetDefaultIMEWnd", "Uint", hwnd)  ; Get IME window handle
        , "UInt", WM_IME_CONTROL                                       ; Message type
        , "Int", IMC_GETOPENSTATUS                                     ; We want to know if IME is open
        , "Int", 0)                                                    ; Not used
}

; Sets the IME status to either direct input or Japanese input mode
; Parameters: ImeStatus - 0 = Direct Input "A", 1 = Japanese Input "あ"
SetImeStatus(ImeStatus) {
    ; Get handle to the active window
    hwnd := WinExist("A")
    
    ; Send a message to the IME system to change its status
    return DllCall("SendMessage"
        , "UInt", DllCall("imm32\ImmGetDefaultIMEWnd", "Uint", hwnd)  ; Get IME window handle
        , "UInt", WM_IME_CONTROL                                       ; Message type
        , "Int", IMC_SETOPENSTATUS                                     ; We want to set IME open status
        , "Int", ImeStatus)                                            ; New status (0 or 1)
}

; Extracts the lower 16 bits from a 32-bit value
; Used to process keyboard layout identifiers
GetLowWord(Word) {
    ; Bitwise AND with a 16-bit mask (2^16 - 1 = 65535)
    return Word & (2 ** 16 - 1)
}

; Gets the keyboard layout ID of the active window
; This helps determine which language is currently active
GetKlid() {
    ; Get the handle of the active window
    WinId := WinExist("A")
    
    ; First get the thread ID of the window, then query its keyboard layout
    return GetLowWord(DllCall("GetKeyboardLayout"
        , "UInt", DllCall("GetWindowThreadProcessId", "UInt", WinId, "UInt", 0)
        , "UInt"))
}

; -------------------------------------------------------------------------
; GUI Helper Functions
; These make our border windows special - they don't take focus or interfere with mouse clicks
; -------------------------------------------------------------------------

; Makes a GUI window never receive focus when clicked
; This prevents the border from stealing focus from your active application
NeverFocusWindow(guiObj) {
    ; Apply the WS_EX_NOACTIVATE extended window style (0x08000000L)
    WinSetExStyle("0x08000000L", guiObj)
    return guiObj
}

; Makes a GUI window "clickthrough" so mouse clicks pass through to windows behind it
; This lets you click through the border as if it wasn't there
MakeClickthrough(guiObj) {
    ; Make sure window is fully opaque (255)
    WinSetTransparent(255, guiObj)
    ; Apply the WS_EX_TRANSPARENT extended window style (+E0x20)
    guiObj.Opt("+E0x20")
    return guiObj
}

; -------------------------------------------------------------------------
; StateBorder Class
; Creates and manages the red border display around the screen
; -------------------------------------------------------------------------

class StateBorder {
    ; Constructor: creates a new border instance
    ; Parameters:
    ;   thickness - How thick the border should be in pixels (default: 10)
    ;   color - Color of the border in hexadecimal (default: 0xDE4D37 - red)
    __New(thickness := 10, color := 0xDE4D37) {
        this.Thickness := thickness  ; Store border thickness
        this.Color := color          ; Store border color
        this.GuiExist := false       ; Track if border is currently displayed
    }

    ; Calculate appropriate thickness based on screen resolution
    ; This scales the border appropriately for high-DPI displays
    static Unit := A_ScreenDPI / 96  ; Scaling factor based on screen DPI
    static DefaultThickness := StateBorder.Unit * 10  ; Default thickness with scaling
    static DefaultColor := 0xDE4D37  ; Default color (bright red)

    ; Method to create and display the border around the screen edges
    Create() {
        ; Don't create if already exists
        if this.GuiExist
            return

        ; Get screen dimensions to size border properly
        screenWidth := A_ScreenWidth
        screenHeight := A_ScreenHeight
        thickness := Round(this.Thickness)  ; Round to nearest pixel

        ; Create top border - spans full width of screen at top edge
        this.TopGUI := Gui("AlwaysOnTop -Caption +ToolWindow")  ; Create a borderless, always-on-top window
        this.TopGUI.BackColor := this.Color  ; Set background to our border color
        this.TopGUI.Show(Format("NA w{1} h{2} x{3} y{4}", 
            screenWidth, thickness, 0, 0))  ; Position and size the window
        NeverFocusWindow(this.TopGUI)       ; Make it never take focus
        MakeClickthrough(this.TopGUI)       ; Make it clickthrough

        ; Create right border - spans full height on right edge
        this.RightGUI := Gui("AlwaysOnTop -Caption +ToolWindow")
        this.RightGUI.BackColor := this.Color
        this.RightGUI.Show(Format("NA w{1} h{2} x{3} y{4}", 
            thickness, screenHeight, screenWidth - thickness, 0))
        NeverFocusWindow(this.RightGUI)
        MakeClickthrough(this.RightGUI)

        ; Create bottom border - spans full width at bottom edge
        this.BottomGUI := Gui("AlwaysOnTop -Caption +ToolWindow")
        this.BottomGUI.BackColor := this.Color
        this.BottomGUI.Show(Format("NA w{1} h{2} x{3} y{4}", 
            screenWidth, thickness, 0, screenHeight - thickness))
        NeverFocusWindow(this.BottomGUI)
        MakeClickthrough(this.BottomGUI)

        ; Create left border - spans full height on left edge
        this.LeftGUI := Gui("AlwaysOnTop -Caption +ToolWindow")
        this.LeftGUI.BackColor := this.Color
        this.LeftGUI.Show(Format("NA w{1} h{2} x{3} y{4}", 
            thickness, screenHeight, 0, 0))
        NeverFocusWindow(this.LeftGUI)
        MakeClickthrough(this.LeftGUI)

        ; Mark that the border now exists
        this.GuiExist := true
    }

    ; Method to remove the border from the screen
    Destroy() {
        ; Don't try to destroy if it doesn't exist
        if !this.GuiExist
            return
            
        ; Remove all four border segments
        this.TopGUI.Destroy()
        this.RightGUI.Destroy()
        this.BottomGUI.Destroy()
        this.LeftGUI.Destroy()
        
        ; Mark that the border no longer exists
        this.GuiExist := false
    }
}

; -------------------------------------------------------------------------
; Language Class 
; Handles language switching and IME state management
; -------------------------------------------------------------------------
  
class Language {
    ; Constructor runs when script loads
    ; If Japanese is active when script starts/reloads, make sure border appears
    static __New() {
        if this._current = this.LangToCode["Japanese"]
            this.border.Create()
    }

    ; State tracking
    static _current := this._GetCurrentLanguageCode()  ; Track current language code
    static border := StateBorder()  ; Create a single border instance for use by this class

    ; Property to get/set current language
    ; Can accept either language name string or language code number
    static Current {
        get => this._current
        set {
            switch Type(value) {
                case "String":  code := this.LangToCode[value]  ; Convert language name to code
                case "Integer": code := value                    ; Use code directly
                default:        throw ValueError("Wrong type passed.")
            }
            this._current := code  ; Store new language code
            this._ChangeLanguage(code)  ; Actually change Windows' language setting
        }
    }

    ; Property to get current language as a word (e.g., "English" or "Japanese")
    static CurrentWord {
        get => this.CodeToLang[this._current]
    }

    ; Maps between language codes and language names
    static CodeToLang := Map(
        "0x4090409", "English",  ; Windows language code for English
        "0x4110411", "Japanese"  ; Windows language code for Japanese
    )

    ; Reverse mapping (language names to codes)
    static LangToCode := Map(
        "English", "0x4090409",
        "Japanese", "0x4110411"
    )

    ; Switch to Japanese input mode
    static ToJapanese() {
        this.Current := "Japanese"  ; Set current language to Japanese
        this.border.Create()  ; Show the red border
        
        ; Give Windows time to update the keyboard layout
        ; Then try to ensure Hiragana mode is active
        SetTimer(() => this._TrySetHiragana(), -200)  ; Try after 200ms delay
    }

    ; Try to activate Hiragana input mode if needed
    static _TrySetHiragana() {
        if (GetKlid() = JA_KLID && GetImeStatus() = 0)  ; If Japanese keyboard but direct input mode
            SetImeStatus(1)  ; Switch to Hiragana input mode
    }

    ; Switch to English input mode
    static ToEnglish() {
        this.Current := "English"  ; Set current language to English
        this.border.Destroy()  ; Remove the red border
    }

    ; Toggle between English and Japanese
    static Toggle() {
        switch this.CurrentWord {
            case "Japanese": this.ToEnglish()   ; If Japanese, switch to English
            case "English": this.ToJapanese()   ; If English, switch to Japanese
        }
    }

    ; Get the Windows code of the current language
    ; This runs once when script loads to determine initial language
    static _GetCurrentLanguageCode() => "0x" Format("{:x}", dllCall("GetKeyboardLayout", "int", 0))

    ; Tell Windows to change the input language
    ; Takes a Windows language code (e.g., "0x4090409" for English)
    static _ChangeLanguage(languageCode) {
        ; Send a Windows message to change keyboard layout
        ; 0x0050 is WM_INPUTLANGCHANGEREQUEST
        try PostMessage(0x0050,, languageCode,, "A")  ; Send to active window
    }
}

; -------------------------------------------------------------------------
; Hotkey Definition
; -------------------------------------------------------------------------

; Win+Space toggles between English and Japanese input modes
#Space::Language.Toggle()  ; # represents the Windows key
