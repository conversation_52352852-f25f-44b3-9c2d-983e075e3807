#Include <Utils\Win>
#Include <Abstractions\Text>
#Include <Tools\Info>
#Include <Extensions\String>
#Include <Paths>

class VsCode {

	static exeTitle := "ahk_exe Code.exe"
	static winTitle := this.exeTitle
	; static winTitle := "Visual Studio Code " this.exeTitle
	static path := "C:\Program Files\Microsoft VS Code\Code.exe"
	; static path := "C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code Insiders\Code - Insiders.exe"

	static winObj := Win({
		winTitle: this.winTitle,
		exePath:  this.path,
	})

	static CloseAllTabs()  => Send("+!w")
	static Reload()        => Send("+!y+!y")
	static CloseTab()      => Send("!w")

}
