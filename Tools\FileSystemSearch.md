# `Tools\FileSystemSearch.ahk` - File System Search Utility

This AutoHotkey class provides a graphical user interface (GUI) for searching files and folders within a specified directory, typically the currently open folder in Windows Explorer. It allows users to input a search query, and then displays matching results in a list view, offering options to open the found items or copy their paths.

## Functionalities

The `FileSystemSearch` class extends the `Gui` class, providing a custom window for displaying search results.

### Class Constructor: `__New(searchWhere?, caseSense := "Off")`

-   **Purpose**: Initializes the search GUI and sets up its properties.
-   **Parameters**:
    -   `searchWhere` (optional): The directory path to search within. If omitted, it attempts to get the path of the currently active Explorer window.
    -   `caseSense` (optional): Specifies whether the search should be case-sensitive. Defaults to "Off".
-   **GUI Setup**: Configures the GUI window with a title, font, dark mode, and a ListView control to display results. It also adds instructional text for the user.
-   **Path Validation**: If `searchWhere` is not provided, it calls `ValidatePath()` to determine the current Explorer path. If no Explorer window is open, it displays an error and exits.
-   **Event Handlers**: Sets up event handlers for ListView double-click (to open file/folder), context menu (to copy path), and GUI resizing and escape key.

### Instance Methods

1.  **`GetInput()`**
    -   **Purpose**: Prompts the user for a search query using an input box.
    -   **Behavior**: It uses `CleanInputBox()` to get user input. If the user cancels the input, the search is aborted.
    -   **Action**: Calls `StartSearch()` with the user's input.

2.  **`ValidatePath()`**
    -   **Purpose**: Determines the search path if not provided in the constructor.
    -   **Behavior**: Attempts to get the title of the active Windows Explorer window, which contains the current directory path. If no Explorer window is found, it displays an error message using `Info()` and exits the script.

3.  **`StartSearch(input)`**
    -   **Purpose**: Executes the file system search based on the provided input.
    -   **Parameter**: `input` - The search string to look for in file and folder names.
    -   **Search Logic**: It loops through files and directories recursively within `this.path`. For each item, it checks if its name contains the `input` string (respecting `caseSense`).
    -   **Performance**: Optimizes ListView updates by disabling redrawing during the search and re-enabling it afterward. It also displays a temporary "Search in progress" message using `Infos()`.
    -   **Result Display**: Adds matching files and folders to the ListView with columns for "File", "Folder", and "Directory".
    -   **GUI Update**: Adjusts ListView column widths to fit content and shows the GUI window.

4.  **`DestroyResultListGui()`**
    -   **Purpose**: Minimizes and destroys the search results GUI.
    -   **Trigger**: Called when the Escape key is pressed.

5.  **`SetOnEvents()`**
    -   **Purpose**: Configures event handlers for the ListView and the main GUI window.
    -   **Events Handled**:
        -   `DoubleClick` on ListView: Calls `ShowResultInFolder()` for the selected item.
        -   `ContextMenu` on ListView: Calls `CopyPathToClip()` for the selected item.
        -   `Size` on GUI: Calls `FixResizing()` to adjust the ListView size when the window is resized.
        -   `Escape` on GUI: Calls `DestroyResultListGui()` to close the window.

6.  **`FixResizing(width, height)`**
    -   **Purpose**: Adjusts the size and position of the ListView control when the main GUI window is resized.

7.  **`ShowResultInFolder(selectedRow)`**
    -   **Purpose**: Opens the selected file or folder in Windows Explorer.
    -   **Behavior**: Uses `Run("explorer.exe /select,")` to open the item and highlight it in Explorer.

8.  **`CopyPathToClip(rowNumber)`**
    -   **Purpose**: Copies the full path of the selected item to the clipboard.
    -   **Behavior**: Displays an "Path copied to clipboard!" message using `Info()`.

9.  **`GetPathFromList(rowNumber)`**
    -   **Purpose**: Constructs the full file or folder path from the data in the ListView row.
    -   **Behavior**: Retrieves the file name, folder name, and directory path from the ListView columns and concatenates them to form the full path.

## Dependencies

The `FileSystemSearch.ahk` class has the following dependencies, included via `#Include` directives:

-   `Extensions\Gui.ahk`: The `FileSystemSearch` class extends this `Gui` class, inheriting its GUI-related functionalities.
-   `Tools\CleanInputBox.ahk`: Used by `GetInput()` to display a clean input box for the search query.
-   `Tools\Info.ahk`: Used for displaying informational messages (e.g., "Open an explorer window first!", "Path copied to clipboard!").
-   `Extensions\String.ahk`: (Not directly used in the provided code snippet, but included).

## Possible Use Cases and Hotkey Demonstrations

`FileSystemSearch.ahk` is designed to be triggered by a hotkey, typically when a user wants to quickly find a file or folder within their current working directory in Explorer.

### Use Case 1: Search in Current Explorer Window

This is the primary use case, allowing a user to press a hotkey, type a search term, and see results from the active Explorer window.

```autohotkey
; Example Hotkey: F6 (as seen in Scr\App\Explorer.ahk)
F6::
{
    ; Creates an instance of FileSystemSearch and immediately prompts for input
    FileSystemSearch().GetInput()
}
return
```

**Explanation**: When `F6` is pressed, a new `FileSystemSearch` object is created. Since no `searchWhere` parameter is passed to the constructor, it automatically detects the path of the currently active Explorer window. Then, `GetInput()` is called, which displays an input box for the user to type their search query. After the user enters the query, the search is performed, and results are displayed in the custom GUI.

### Use Case 2: Search a Specific Directory (Advanced)

While the primary use case is for the current Explorer window, you could theoretically create a hotkey to search a predefined directory.

```autohotkey
; Example Hotkey: Ctrl + Alt + F (Search in a specific project folder)
^!f::
{
    specificPath := "C:\Users\<USER>\Documents\MyDevelopmentProject"
    ; Create an instance and start the search directly with a predefined input
    ; Or, you could still use GetInput() if you want to prompt for the search term
    searcher := new FileSystemSearch(specificPath)
    searcher.GetInput() ; Still prompts for input, but searches in specificPath

    ; Alternatively, to search for a fixed term without prompting:
    ; searcher.StartSearch("report")
}
return
```

**Explanation**: This hotkey would create a `FileSystemSearch` instance specifically for `C:\Users\<USER>\Documents\MyDevelopmentProject`. When `GetInput()` is called, the input box will appear, and the search will be confined to the specified `specificPath`.

## How Other Files Use This AHK

As identified by the search, `Tools\FileSystemSearch.ahk` is directly used by `Scr\App\Explorer.ahk`.

**`Scr\App\Explorer.ahk`**

```autohotkey
#Include <Tools\FileSystemSearch>

; ... other code ...

F6::FileSystemSearch().GetInput()

; ... other code ...
```

This snippet from `Scr\App\Explorer.ahk` demonstrates the primary way `FileSystemSearch` is integrated into the system:

-   `#Include <Tools\FileSystemSearch>`: This line makes the `FileSystemSearch` class available for use within `Scr\App\Explorer.ahk`.
-   `F6::FileSystemSearch().GetInput()`: This hotkey definition directly creates a new instance of `FileSystemSearch` and immediately calls its `GetInput()` method. This is a concise way to trigger the file search functionality when the `F6` key is pressed, assuming an Explorer window is active.

This integration highlights that `FileSystemSearch` is intended to be a user-triggered utility, providing on-demand file search capabilities within the context of an active Explorer window. It acts as a quick, in-place search tool that complements the native Windows Explorer functionalities.
