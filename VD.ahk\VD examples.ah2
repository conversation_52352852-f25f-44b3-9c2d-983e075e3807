;you should first Run this, then Read this
;Ctrl + F: jump to #useful stuff

;#SETUP START
#SingleInstance force
ListLines 0
SendMode "Input"
SetWorkingDir A_ScriptDir
KeyHistory 0
#WinActivateForce

ProcessSetPriority "H"

SetWinDelay -1
SetControlDelay -1


;START of gui stuff
myGui:=Gui()
myGui.SetFont("s12", "Segoe UI")
explanation:="
(
0 to pin this Window on all desktops
you can spam (2,1,2,1) for fun

here's a challenge (you might lose this window):
Unpin this using 0
go to Desktop 3 (3)
this time, use Win + * to come back to this window wherever you are
(and wherever this window is)
so you can move this window to desktop 2 (5), you go to desktop 1, and use Win + *
(if you want to search in this script, the hotkey is #*)

9 to throw a window to Desktop 3 (and not follow it)

getters:
f1 to see which desktop you currently are in
f6 to see which desktop this window is in
f2 to see the total number of virtual desktops

(You might want to pin this window for this part):
!+ (Alt + +) to createDesktop and go to it
f1 to see which desktop you currently are in

but at this point, just use Win + Tab..
these functions are mostly for script only,
for example: I used VD.createUntil(3)
at the start of this tutorial, to make sure we have at least 3 VD

^++ (Ctrl Alt + +) to create until you have 3 desktops
!- (Alt + -) to remove the current desktop
^+- (Ctrl ALt + -) to delete the 3rd desktop

more below, look at the hotkeys in code.
)"
Explanation_Edit:=myGui.add("Edit", "-vscroll -E0x200", explanation) ; https://www.autohotkey.com/boards/viewtopic.php?t=3956#p21359
;deselect edit text BY moving caret to start
Postmessage 0xB1,0,0,, "ahk_id " Explanation_Edit.hwnd
myGui.title:="VD.ahk examples WinTitle"
myGui.show()
;END of gui stuff

;include the library
#Include %A_LineFile%\..\VD.ah2

;#SETUP END

VD.createUntil(3) ;create until we have at least 3 VD

return

;#useful stuff
1::VD.goToDesktopNum(1)
2::VD.goToDesktopNum(2)
3::VD.goToDesktopNum(3)

;follow your window
4::VD.MoveWindowToDesktopNum("A",1).follow()
5::VD.MoveWindowToDesktopNum("A",2).follow()
6::VD.MoveWindowToDesktopNum("A",3).follow()

;just move window
7::VD.MoveWindowToDesktopNum("A",1)
8::VD.MoveWindowToDesktopNum("A",2)
9::VD.MoveWindowToDesktopNum("A",3)

; wrapping / cycle back to first desktop when at the last
^+#left::VD.goToRelativeDesktopNum(-1)
^+#right::VD.goToRelativeDesktopNum(+1)

; move window to left and follow it
#!left::VD.MoveWindowToRelativeDesktopNum("A", -1).follow()
; move window to right and follow it
#!right::VD.MoveWindowToRelativeDesktopNum("A", 1).follow()

;to come back to this window
#*::{ 
    VD.goToDesktopOfWindow("VD.ahk examples WinTitle")
    ; VD.goToDesktopOfWindow("ahk_exe code.exe")
}

;getters and stuff
f6::{
    Msgbox VD.getDesktopNumOfWindow("VD.ahk examples WinTitle")
    ; Msgbox VD.getDesktopNumOfWindow("ahk_exe GitHubDesktop.exe")
}
f1::Msgbox VD.getCurrentDesktopNum()
f2::Msgbox VD.getCount()

;Create/Remove Desktop
!+::VD.createDesktop(true) ;go to newly created
#+::VD.createDesktop(false) ;don't go to newly created, also the default

!-::VD.removeDesktop(VD.getCurrentDesktopNum())
#!-::VD.removeDesktop(VD.getCount()) ;removes 3rd desktop if there are 3 desktops

; Choose one of these implementations:
^+-::VD.createUntil(3) ;create until we have at least 3 VD

; Or use a different hotkey for the second implementation:
^+_::{
    VD.createUntil(3) ;create until we have at least 3 VD
    sleep 1000
    ;FALLBACK IS ONLY USED IF YOU ARE CURRENTLY ON THAT VD
    VD.removeDesktop(3, 1)
}

;Pin Window
0::VD.TogglePinWindow("A")
^0::VD.PinWindow("A")
!0::VD.UnPinWindow("A")
#0::MsgBox VD.IsWindowPinned("A")

;Pin App
.::VD.TogglePinApp("A")
^.::VD.PinApp("A")
!.::VD.UnPinApp("A")
#.::MsgBox VD.IsAppPinned("A")

f3::Exitapp
