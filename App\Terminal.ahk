#Include <Utils\Win>


class Obsidian{

  static exeTitle := "ahk_exe Obsidian.exe"
  static winTitle := this.exeTitle
    static path := "C:\Users\<USER>\AppData\Local\Programs\Obsidian\Obsidian.exe"

  static winObj := Win({
    winTitle: this.winTitle,
    exePath: this.path,
  })
}
; class Zen_h{

; 	static exeTitle := "ahk_exe zen.exe"
; 	static winTitle := this.exeTitle
;     static path := "C:\zen\zen-h\app\win\zen.exe"

; 	static winObj := Win({
; 		winTitle: this.winTitle,
; 		exePath: this.path,
; 	})
; }
class Chrome{

  static exeTitle := "ahk_exe chrome.exe"
  static winTitle := "Google Chrome " this.exeTitle
    static path := "C:\Program Files\Google\Chrome\Application\chrome.exe"

  static winObj := Win({
    winTitle: this.winTitle,
    exePath: this.path,
  })
}
class FilePilot{

  static exeTitle := "ahk_exe FPilot.exe"
  static winTitle := this.exeTitle
    static path := "C:\Users\<USER>\AppData\Local\Voidstar\FilePilot\FPilot.exe"

  static winObj := Win({
    winTitle: this.winTitle,
    exePath: this.path,
  })
}
class Sublime {

  static exeTitle := "ahk_exe sublime_text.exe"
  static winTitle := this.exeTitle
    ; static path := "C:\Program Files\Sublime Text\sublime_text.exe"
    static path := "C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\sublime_text.exe.lnk"

  static winObj := Win({
    winTitle: this.winTitle,
    exePath: this.path,
  })
}
class Terminal {

  static exeTitle := "ahk_exe WindowsTerminal.exe"
  ; static class    := "ahk_class CASCADIA_HOSTING_WINDOW_CLASS"
  static winTitle := this.exeTitle
  ; static excludeTitle := "Cmd( Admin)?|PowerShell( Admin)?"
  ; static path := "C:\Program Files\WindowsApps\Microsoft.WindowsTerminalPreview_1.23.10353.0_x64__8wekyb3d8bbwe\wt.exe"
  ; Using the shell:AppsFolder protocol is more reliable for accessing Windows Store apps
    static path := "explorer.exe shell:AppsFolder\Microsoft.WindowsTerminalPreview_8wekyb3d8bbwe!App"

  static winObj := Win({
    winTitle: this.winTitle,
    exePath: this.path,
    ; excludeTitle: this.excludeTitle
  })

  ; static DeleteWord() => Send("^w")
}