#Requires AutoHotkey v2.0
; IME control functions

; Windows message constants for IME control
WM_IME_CONTROL := 0x0283
IMC_GETOPENSTATUS := 0x0005
IMC_SETOPENSTATUS := 0x0006

; Gets the current IME status (0 = Direct Input "A", 1 = Japanese Input "あ")
GetImeStatus() {
    hwnd := WinExist("A")
    
    return DllCall("SendMessage"
        , "UInt", DllCall("imm32\ImmGetDefaultIMEWnd", "Uint", hwnd)
        , "UInt", WM_IME_CONTROL
        , "Int", IMC_GETOPENSTATUS
        , "Int", 0)
}

; Sets the IME status (0 = Direct Input "A", 1 = Japanese Input "あ")
SetImeStatus(ImeStatus) {
    ; In v2, we need to get the active window handle differently
    hwnd := WinExist("A")
    
    return DllCall("SendMessage"
        , "UInt", DllCall("imm32\ImmGetDefaultIMEWnd", "Uint", hwnd)
        , "UInt", WM_IME_CONTROL
        , "Int", IMC_SETOPENSTATUS
        , "Int", ImeStatus)
}
;====== Keyboard Layout ID functions ==============

; Japanese keyboard layout ID
JA_KLID := 0x411

; Extracts the lower 16 bits from a 32-bit value
GetLowWord(Word) {
    return Word & (2 ** 16 - 1)
}

; Gets the keyboard layout ID of the active window
GetKlid() {
    WinId := WinExist("A")
    
    return GetLowWord(DllCall("GetKeyboardLayout"
        , "UInt", DllCall("GetWindowThreadProcessId", "UInt", WinId, "UInt", 0)
        , "UInt"))
}
