
# AutoHotkey Library Overview

This repository contains a comprehensive AutoHotkey (AHK) v2 library designed for Windows automation. It is structured into several modules, each providing specific functionalities, from basic window management to complex UI automation and OCR.

## Core Concepts

The library is built around a modular architecture, with scripts organized into directories based on their purpose. Key features include:

- **Abstraction:** A set of scripts that provide simplified interfaces for common actions like copy, paste, and window manipulation.
- **Application-Specific Logic:** Dedicated scripts for interacting with various applications such_ as browsers, media players, and development tools.
- **System-Level Control:** Modules for managing system settings like brightness, language, and power options.
- **UI Automation (UIA):** A powerful implementation of Microsoft's UI Automation framework, enabling interaction with application interfaces.
- **OCR:** A wrapper for the UWP OCR library, allowing for text recognition from the screen.
- **Virtual Desktops:** A comprehensive module for managing virtual desktops in Windows.
- **Utilities:** A collection of helper scripts for tasks like image searching, color manipulation, and more.

## Key Modules

### Abstractions

The `Abstractions` directory contains scripts that simplify common operations. For example, `Base.ahk` provides functions like `Copy()`, `Paste()`, and `CloseTab()` that send standard keyboard shortcuts.

### App

The `App` directory contains scripts tailored for specific applications. Each script provides functions and hotkeys for interacting with a particular program, such as a web browser (`Browser.ahk`), Spotify (`Spotify.ahk`), or Visual Studio Code (`VsCode.ahk`).

### System

The `System` directory provides tools for controlling the operating system. This includes scripts for adjusting screen brightness (`Brightness.ahk`), managing system language (`Language.ahk`), and performing power operations (`System.ahk`).

### UIA

The `UIA.ahk` script is a comprehensive implementation of Microsoft's UI Automation framework. It allows for advanced interaction with application user interfaces, enabling scripts to read and control UI elements. This is a powerful tool for automating applications that do not provide a traditional API.

### OCR

The `OCR` module provides text recognition capabilities using the built-in Windows OCR engine. It can extract text from images, windows, or specific regions of the screen. This is particularly useful for automating applications that do not expose their text content through other means.

### VD.ahk

The `VD.ahk` script provides a complete solution for managing virtual desktops. It allows for creating, deleting, and switching between desktops, as well as moving windows between them.

### Utils

The `Utils` directory contains a variety of utility scripts that provide helpful functions for other parts of the library. This includes tools for image searching (`Image.ahk`), color manipulation, and more.

## Startup Script

The main entry point for this library is `Scr/Keys/Hotkeys.ahk`. This script defines the hotkeys that are active on startup and loads the necessary dependencies from the other modules. The hotkeys are designed to provide a consistent and efficient workflow for managing windows, applications, and the system as a whole.
