# Hotkeys and Dependencies

This document provides a detailed breakdown of the hotkeys defined in `Scr/Keys/Hotkeys.ahk` and their dependencies. Each hotkey is listed with its intended functionality and the other scripts it relies on to perform its action.

## Hotkey Reference

- **`+WheelDown`**: Scrolls down 7 lines at a time.
  - **Dependencies**: None

- **`+WheelUp`**: Scrolls up 7 lines at a time.
  - **Dependencies**: None

- **`#!y`**: Suspends the script with a popup showing the status.
  - **Dependencies**: `Abstractions/Script.ahk`

- **`#!-`**: Reloads the script.
  - **Dependencies**: `Abstractions/Script.ahk`

- **`#!r`**: Reboots the system.
  - **Dependencies**: `System/System.ahk`

- **`#!s`**: Puts the system to sleep.
  - **Dependencies**: `System/System.ahk`

- **`#!=`**: Powers down the system.
  - **Dependencies**: `System/System.ahk`

- **`#!v`**: Opens Windows Defender Protection History.
  - **Dependencies**: None

- **`!+b`**: Launches or activates the main browser.
  - **Dependencies**: `App/Browser.ahk`, `Utils/Win.ahk`

- **`!+v`**: Launches or activates Visual Studio Code.
  - **Dependencies**: `App/VsCode.ahk`, `Utils/Win.ahk`

- **`!+x`**: Launches or activates Obsidian.
  - **Dependencies**: `App/Obsidian.ahk`, `Utils/Win.ahk`

- **`!+a`**: Launches or activates the terminal.
  - **Dependencies**: `App/Terminal.ahk`, `Utils/Win.ahk`

- **`!+s`**: Launches or activates Sublime Text.
  - **Dependencies**: `App/Sublime.ahk`, `Utils/Win.ahk`

- **`!+e`**: Launches or activates FilePilot.
  - **Dependencies**: `App/FilePilot.ahk`, `Utils/Win.ahk`

- **`!+7`**: Launches a specific application.
  - **Dependencies**: None

- **`!+8`**: Launches a specific application.
  - **Dependencies**: None

- **`!+9`**: Launches a specific application.
  - **Dependencies**: None

- **`!+w`**: Closes the active application by sending Alt+F4.
  - **Dependencies**: None

- **`!+[`**: Maximizes the active window.
  - **Dependencies**: None

- **`!+]`**: Restores the active window.
  - **Dependencies**: None

- **`#Space`**: Toggles the system language, if not in an AutoHotkey window.
  - **Dependencies**: `System/Language.ahk`

- **`#^LButton`**: Displays coordinate information.
  - **Dependencies**: `Tools/CoordInfo.ahk`

- **`#+LButton`**: Displays relative coordinate information.
  - **Dependencies**: `Tools/RelativeCoordInfo.ahk`

- **`#y`**: Displays window information.
  - **Dependencies**: `Tools/WindowInfo.ahk`

- **`#0`**: Shows the most recent screenshot.
  - **Dependencies**: `Tools/HoverScreenshot.ahk`

- **`#k`**: Triggers a Google search.
  - **Dependencies**: `Tools/InternetSearch.ahk`

- **`#c`**: Captures a screen snip with OCR.
  - **Dependencies**: `OCR/ScreenSnipOCR.ahk`

- **`OnClipboardChange`**: Listens for a specific string in the clipboard to trigger a tab split in the browser.
  - **Dependencies**: `App/Browser.ahk`
