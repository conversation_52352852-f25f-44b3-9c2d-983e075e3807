; ======================================================================================================================
; General Purpose Key Chorder
; ======================================================================================================================
;
; This script implements a versatile two-layer hotkey system, triggered by the Win+H combination. It acts as a
; centralized launcher for a variety of common tasks, from opening websites and applications to running utility functions.
;
; How it works:
; 1. Press the primary hotkey: #h (Win + H).
; 2. The script then waits for a second, single-key input from the user.
; 3. This second key is mapped to a specific action in the `actions` map defined below.
; 4. The actions can be:
;    - Opening a specific website (e.g., 'm' for Gmail, 'g' for GitHub).
;    - Launching or activating an application (e.g., 'd' for DS4Windows, 's' for Steam).
;    - Executing a utility function (e.g., 'k' to get a keycode, 'j' to search for an emoji).
;
; This chording approach provides a large number of easily accessible shortcuts without cluttering the global
; hotkey namespace.
;
; ======================================================================================================================

#Include <Tools\CleanInputBox>
#Include <App\Autohotkey>
#Include <Tools\KeycodeGetter>
#Include <Misc\EmojiSearch>
#Include <Utils\InputOutput>
#Include <App\Browser>
; #Include <App\Steam>
; #Include <App\DS4>
#Include <Abstractions\Registers>
#Include <Converters\Layouts>
; This is the primary hotkey (Win+H) that initiates the key chording sequence.
#h:: {
    ; Defines the set of valid secondary keys that can be pressed after Win+H.
  sValidKeys := Registers.ValidRegisters "[]\{}|-=_+;:'`",<.>/?"
    ; Prompts the user for a single key press and validates it against the allowed keys.
  try key := Registers.ValidateKey(GetInput("L1", "{Esc}").Input, sValidKeys)
  catch UnsetItemError {
        ; If the input is invalid or Esc is pressed, cancel the action.
    Registers.CancelAction()
    return
  }

    ; Internal helper function to get user input and display it in an Info window.
  static _ShowInInfo() {
    if !input := CleanInputBox().WaitForInput()
      return
    Infos(input)
  }

    ; This map defines the association between the secondary key and the action to be performed.
  static actions := Map(
; The Links object is defined in /Lib/Links.ahk, a Map that contains various URLs
; Because Scr/Keys/Hotkeys.ahk includes Links.ahk, the Links object becomes
;   available globally to all other scripts that are also included by
;   Scr/Keys/Hotkeys.ahk, such as Scr/GeneralKeyChorder.ahk. This is how
;   AutoHotkey's include mechanism works: once a file is included, its contents
;   (variables, functions, classes) are available throughout the entire script.

    "m", () => Browser.RunLink(Links["gmail"]),          ; Opens Gmail
    "n", () => Browser.RunLink(Links["monkeytype"]),      ; Opens Monkeytype for typing tests
    "g", () => Browser.RunLink(Links["my github"]),       ; Opens your GitHub page
    "f", () => Browser.RunLink(Links["skill factory"]),   ; Opens Skill Factory website
    "x", () => Browser.RunLink(Links["regex"]),           ; Opens a regex testing website
    "w", () => Browser.RunLink(Links["wildberries"]),     ; Opens Wildberries online store
    "d", () => DS4.winObj.App(),                           ; Launches or activates DS4Windows
    "s", () => Steam.winObj.App(),                         ; Launches or activates Steam
    "a", () => Browser.RunLink(Links["ahk v2 docs"]),     ; Opens the AutoHotkey v2 documentation
    "r", () => Browser.RunLink(Links["reddit"]),          ; Opens Reddit
    "T", () => Browser.RunLink(Links["twitch"]),          ; Opens Twitch
    "h", () => Browser.RunLink(Links["phind"]),           ; Opens Phind search engine
    "j", () => EmojiSearch(CleanInputBox().WaitForInput()), ; Prompts for input and searches for an emoji
    "c", () => Infos(A_Clipboard),                         ; Displays the current clipboard content in an Info window
    "k", KeyCodeGetter,                                   ; Runs the KeyCodeGetter tool
    "i", _ShowInInfo,                                     ; Prompts for input and shows it in an Info window
    "v", () => Browser.RunLink(Links["vk"]),              ; Opens VK.com
    "t", () => Browser.RunLink(Links["mastodon"]),        ; Opens Mastodon
    "e", () => Browser.RunLink(Links["gogoanime"]),       ; Opens Gogoanime website
    "u", () => Infos(GetWeather()),                       ; Gets and displays the weather

  )
    ; If a valid key was pressed, execute the corresponding action from the map.
  if key
    try actions[key].Call()
}