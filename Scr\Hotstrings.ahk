; ======================================================================================================================
; Dynamic and Static Hotstring Management
; ======================================================================================================================
;
; This script defines and manages a collection of hotstrings, categorized into two main types:
; 1. Dynamic Hotstrings: These generate content programmatically at the time of expansion.
; 2. Static Hotstrings: These expand to a predefined, fixed string.
;
; The script uses the `Hotstringer` utility class to process and activate these hotstrings.
; When the hotkey `#sc34` (which corresponds to `Alt + .` on a standard keyboard layout) is pressed,
; the script configures the `Hotstringer` with the defined hotstring maps and then initiates it.
;
; This allows for quick insertion of frequently used text, dynamic data (like current date/time),
; or even random content, enhancing typing efficiency and reducing repetitive tasks.
;
; ======================================================================================================================

#Include <Utils\Hotstringer>
#Include <Tools\CleanInputBox>
#Include <Converters\DateTime>
#Include <Utils\LangDict>
#Include <Utils\CharGenerator>
#Include <Environment>
#Include <Utils\ClipSend>

; Hotkey: Alt + . (on a standard keyboard layout)
; This hotkey triggers the setup and initiation of the hotstring system.
#sc34:: {
  ; Map of dynamic hotstrings. The value is a function that will be called to generate the replacement text.
  static DynamicHotstrings := Map(

    "radnum", () => Random(1000000, 9999999), ; Expands to a random 7-digit number
    "date",   () => DateTime.Date,             ; Expands to the current date
    "week",   () => DateTime.WeekDay,          ; Expands to the current day of the week
    "time",   () => DateTime.Time,             ; Expands to the current time
    "dt",     () => DateTime.Date " " DateTime.Time, ; Expands to current date and time
    "dw",     () => DateTime.Date " " DateTime.WeekDay, ; Expands to current date and day of week
    "dwt",    () => DateTime.Date " " DateTime.WeekDay " " DateTime.Time, ; Expands to current date, day of week, and time
    "uclanr", () => GetRandomWord("english") " ", ; Expands to a random English word
    "ilandh", () => GetRandomWord("russian") " ", ; Expands to a random Russian word
    "chrs",   () => CharGenerator(2).GenerateCharacters(15), ; Expands to 15 random characters

  )
  ; Map of static hotstrings. The value is a fixed string that will replace the hotstring.
  static StaticHotstrings := Map(

    ;; Words - common misspellings or frequently typed words

    "imm",  "immediately ", ; it's a hard word okay!
    "thu",  "thumbnail ",
    "imp",  "implement ",
    "uni",  "uninterest ",
    "abb",  "abbreviation",
    "unf",  "unfavorite ",
    "aba",  "abbrev-alias ",
    "cata", "cataclysmic ",

    ;; Nicknames - personal or common online handles

    "micha",    "Micha-ohne-el",
    "reiwa",    "rbstrachan",
    "anon",     "anonymous1184",
    "geekdude", "G33kDude",
    "me",       "Axlefublr",
    "wew",      "710902092457312266", ; Likely a Discord ID or similar

    ;; Command line - frequently used commands or code snippets

    "C",     "C# en-us",
    "vimap", "vim.keymap.set()",

  )
  ; Assign the defined hotstring maps to the Hotstringer utility.
  Hotstringer.DynamicHotstrings := DynamicHotstrings
  Hotstringer.StaticHotstrings := StaticHotstrings
  ; Define additional characters that can end a hotstring (e.g., Enter, Tab, Space).
  Hotstringer.EndKeys .= "{Enter}{Tab} "
  ; Initiate the Hotstringer to activate all defined hotstrings.
  Hotstringer.Initiate()
}
