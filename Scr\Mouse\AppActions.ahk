#Include <Utils\Press>
#Include <App\Terminal>
#Include <App\Explorer>
#Include <App\VsCode>
#Include <App\Browser>
#Include <Utils\Win>
#Include <Abstractions\Base>

#MaxThreadsBuffer true

Media_Stop:: {
	sections := Press.GetSections()
	switch {
		case sections.topRight:    GroupDeactivate("Main")
		case sections.bottomRight
			&& WinExist(Browser.Chat.winTitle): Browser.Chat.winObj.MinMax()
		case sections.topLeft:     Terminal.winObj.App()
		case sections.bottomLeft:  Explorer.winObj.MinMax()
		case sections.left:        VsCode.winObj.App()
		case sections.up:          Browser.winObj.App()
		default:                   AltTab()
	}
}

#MaxThreadsBuffer false