; ======================================================================================================================
; Quick Folder Navigation for Windows Explorer
; ======================================================================================================================
;
; This script provides a two-layer hotkey system to rapidly open predefined folders in Windows Explorer.
;
; How it works:
; 1. Press the primary hotkey: Alt + D.
; 2. The script will then wait for a second, single-key input from the user.
; 3. This second key corresponds to a specific folder shortcut defined in the `keyActions` map below.
;    For example, after pressing Alt+D, pressing 'q' will open the "Volume" folder, 'v' will open "Pictures", etc.
; 4. The script uses the `App/Explorer.ahk` application-specific setup, which pre-configures window objects (`Win` objects)
;    for each folder path. This allows the script to either focus the folder's window if it's already open or
;    run a new Explorer instance at that path if it's not.
;
; The entire functionality is only active when `Environment.VimMode` is set to `false`, as controlled by the
; `#HotIf !Environment.VimMode` directive. This prevents conflicts with other modes of operation.
;
; ======================================================================================================================

#Include <Utils\GetInput>
#Include <App\Explorer>
#Include <Utils\Win>
#Include <Abstractions\Registers>
#Include <Environment>

; This directive ensures that the hotkeys below are only active when VimMode is disabled.
#HotIf !Environment.VimMode

; This is the primary hotkey (Alt+D) that initiates the folder navigation sequence.
<!d:: {
  ; Defines the set of valid secondary keys that can be pressed after Alt+D.
  sValidKeys := Registers.ValidRegisters "[]\{}|-=_+;:'`",<.>/?"
  ; Prompts the user for a single key press and validates it against the allowed keys.
  try key := Registers.ValidateKey(GetInput("L1", "{Esc}").Input, sValidKeys)
  catch UnsetItemError {
    ; If the input is invalid or Esc is pressed, cancel the action.
    Registers.CancelAction()
    return
  }

  ; Helper function to open or activate an Explorer window for a given directory shortcut.
  ; @param dir The key corresponding to the desired folder in `Explorer.WinObjs`.
  ; @param folders The method to call on the Win object, defaults to `_Folders` which uses the `App_Folders` method.
  static SetupExplorer(dir, folders := "_Folders") {
    Explorer.WinObjs.%dir%.App%folders%()
  }

  ; This map defines the association between the secondary key and the folder to be opened.
  static keyActions := Map(

    "q", () => SetupExplorer("Volume"),
    "v", () => SetupExplorer("Pictures"),
    "r", () => SetupExplorer("Tree"),
    "t", () => SetupExplorer("VideoTools"),
    "s", () => SetupExplorer("Memes"),
    "e", () => SetupExplorer("Emoji"),
    "a", () => SetupExplorer("Audio"),
    "w", () => SetupExplorer("ScreenVideos"),
    "d", () => SetupExplorer("PC", ""), ; Opens "This PC"
    "c", () => SetupExplorer("Content"),
    "o", () => SetupExplorer("Other"),
    "O", () => SetupExplorer("OnePiece"),
    "u", () => SetupExplorer("User", ""), ; Opens the User's home directory
    "l", () => SetupExplorer("Logos"),
    "h", () => SetupExplorer("Themes"),
    "p", () => SetupExplorer("Prog"),
    "n", () => SetupExplorer("Downloaded"),
    "1", () => Explorer.winObj.CloseAll(), ; Closes all open Explorer windows

  )
  ; If a valid key was pressed, execute the corresponding action from the map.
  if key
    try keyActions[key].Call()
}

; End of the conditional hotkey section.
#HotIf