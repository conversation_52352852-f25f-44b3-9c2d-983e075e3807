# `App\Git.ahk` - Git Automation Class

This AutoHotkey class provides a programmatic interface for interacting with Git repositories, allowing for automation of common Git operations like adding files, committing changes, and pushing to a remote repository. It also includes a static method for generating GitHub links for specific files or folders within a repository.

## Functionalities

The `Git` class encapsulates Git commands and provides a convenient way to chain them together before execution.

### Class Constructor: `__New(workingDir)`

-   **Purpose**: Initializes a new `Git` object, setting the working directory for Git commands.
-   **Parameter**:
    -   `workingDir`: The absolute path to the Git repository's working directory.
-   **Internal**: It creates an instance of the `Cmd` class (from `Utils\Cmd`) to execute shell commands within the specified `workingDir`.

### Instance Methods

1.  **`Add(files*)`**
    -   **Purpose**: Stages files for the next commit.
    -   **Parameters**: `files*` (variadic) - One or more file paths (relative to the `workingDir`) to add. If no files are provided, it stages all changes (`git add .`).
    -   **Returns**: The `Git` object itself, allowing for method chaining.
    -   **Git Command**: `git add <file1> <file2> ...` or `git add .`

2.  **`Commit(message)`**
    -   **Purpose**: Records staged changes to the repository.
    -   **Parameter**: `message` - The commit message as a string.
    -   **Returns**: The `Git` object itself, allowing for method chaining.
    -   **Git Command**: `git commit -m "message"`

3.  **`Push()`**
    -   **Purpose**: Uploads local repository changes to a remote repository.
    -   **Returns**: The `Git` object itself, allowing for method chaining.
    -   **Git Command**: `git push`

4.  **`Execute()`**
    -   **Purpose**: Executes all the Git commands that have been chained using `Add`, `Commit`, and `Push`.
    -   **Returns**: The `Git` object itself.
    -   **Behavior**: After execution, the internal list of commands is cleared.

### Static Method

1.  **`static Link(path)`**
    -   **Purpose**: Generates a GitHub URL for a given file or folder path. This is useful for quickly navigating to the corresponding location on GitHub.
    -   **Parameter**: `path` - The absolute path to the file or folder. The method intelligently determines if it's a file or folder and constructs the appropriate GitHub URL (e.g., using `/blob/main/` for files and `/tree/main/` for folders). It also handles path conversions (e.g., replacing backslashes with forward slashes).
    -   **Dependencies**: Relies on a static `github` link defined in the `Links` object (from `Links.ahk`) and `Paths.Prog` and `Paths.Lib` (from `Paths.ahk`) for path manipulation.
    -   **Returns**: A string representing the GitHub URL.

## Dependencies

The `Git.ahk` class has the following dependencies, included via `#Include` directives:

-   `Paths.ahk`: Used for defining program and library paths, which are then used by the `Link` static method for path manipulation.
-   `Tools\Info.ahk`: (Not directly used in the provided code snippet, but included).
-   `Abstractions\Text.ahk`: (Not directly used in the provided code snippet, but included).
-   `Utils\Cmd.ahk`: Crucial for executing Git commands. The `Git` class instantiates `Cmd` to run shell commands.
-   `Links.ahk`: Used by the `Link` static method to retrieve the base GitHub URL.
-   `System\Web.ahk`: (Not directly used in the provided code snippet, but included).

## Possible Use Cases and Hotkey Demonstrations

Since `Git.ahk` provides a class, it's designed to be instantiated and used within other AutoHotkey scripts. Here are some conceptual examples of how you might integrate it with hotkeys:

### Use Case 1: Quick Commit and Push

Automate the process of adding all changes, committing with a predefined message, and pushing to the remote.

```autohotkey
; Example Hotkey: Ctrl + Alt + G
^!g::
{
    ; Replace with the actual path to your repository
    repoPath := "C:\Users\<USER>\Documents\MyProject"
    commitMessage := "Quick commit: Automated changes"

    ; Create a Git object for the repository
    git := new Git(repoPath)

    ; Chain the commands: add all, commit, then push, and execute
    git.Add().Commit(commitMessage).Push().Execute()

    MsgBox("Changes committed and pushed for " repoPath)
}
return
```

### Use Case 2: Commit Selected Files

A hotkey to commit only specific files, prompting for a commit message.

```autohotkey
; Example Hotkey: Ctrl + Shift + C
^+c::
{
    repoPath := "C:\Users\<USER>\Documents\MyProject"

    ; Prompt the user for files to commit (e.g., currently selected files in Explorer)
    ; For demonstration, let's assume you have a way to get selected files
    ; In a real scenario, you might use A_Clipboard, or a GUI to select files.
    InputBox, filesToCommit, Commit Files, Enter file paths (space-separated, relative to repo root):
    if (ErrorLevel) ; User cancelled
        return

    InputBox, commitMessage, Commit Message, Enter your commit message:
    if (ErrorLevel) ; User cancelled
        return

    git := new Git(repoPath)

    ; Split the input string into an array of files
    fileArray := StrSplit(filesToCommit, " ")

    ; Add the specified files, commit, and execute
    git.Add(fileArray*).Commit(commitMessage).Execute()

    MsgBox("Selected files committed for " repoPath)
}
return
```

### Use Case 3: Open GitHub Link for Current File

A hotkey to generate and open the GitHub link for the currently active file in an editor (e.g., VS Code).

```autohotkey
; Example Hotkey: Ctrl + Alt + L
^!l::
{
    ; This part would depend on your editor.
    ; For VS Code, you might get the active file path from its API or window title.
    ; For demonstration, let's use a placeholder.
    activeFilePath := "C:\Users\<USER>\Documents\MyProject\src\main.js"

    ; Generate the GitHub link using the static method
    githubLink := Git.Link(activeFilePath)

    ; Open the link in the default browser
    Web.Open(githubLink) ; Assumes Web.ahk has an Open method

    MsgBox("Opened GitHub link for: " activeFilePath "`n" githubLink)
}
return
```

## How Other Files Use This AHK

Based on the search, `App\Git.ahk` is not directly included using `#Include` directives in other `.ahk` files within the provided directory structure. This indicates that it's designed to be used as a standalone class that is instantiated when its functionalities are required.

For example, another script would create an instance of the `Git` class like this:

```autohotkey
#Include <App\Git> ; This line would be needed in the script that uses the Git class

myRepoGit := new Git("C:\path\to\my\repo")
myRepoGit.Add("file1.txt", "folder/file2.ahk").Commit("Added new files").Push().Execute()
```

The `Git.Link()` static method can be called directly without instantiating the class:

```autohotkey
#Include <App\Git> ; This line would be needed in the script that uses the Git class

link := Git.Link("C:\Users\<USER>\Documents\MyProject\README.md")
MsgBox(link)
```